<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ExternalMailService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\File;
use Inertia\Inertia;

/**
 * 郵件設定管理控制器
 */
class MailSettingsController extends Controller
{
    /**
     * 顯示郵件設定頁面
     */
    public function index()
    {
        $currentSettings = [
            'external_api_enabled' => config('mail.external_api.enabled', false),
            'external_api_url' => config('mail.external_api.url'),
            'external_api_account' => config('mail.external_api.account'),
            'external_api_reply_to' => config('mail.external_api.reply_to'),
            'mail_mailer' => config('mail.default'),
            'mail_from_address' => config('mail.from.address'),
            'mail_from_name' => config('mail.from.name'),
        ];

        return Inertia::render('Admin/MailSettings', [
            'settings' => $currentSettings,
            'canTestApi' => !empty($currentSettings['external_api_url']) && 
                          !empty($currentSettings['external_api_account']),
        ]);
    }

    /**
     * 更新郵件設定
     */
    public function update(Request $request)
    {
        $request->validate([
            'external_api_enabled' => 'required|boolean',
            'external_api_url' => 'required_if:external_api_enabled,true|url',
            'external_api_account' => 'required_if:external_api_enabled,true|string',
            'external_api_password' => 'nullable|string',
            'external_api_reply_to' => 'required_if:external_api_enabled,true|email',
        ]);

        try {
            $envPath = base_path('.env');
            $envContent = File::get($envPath);

            // 更新 .env 檔案
            $updates = [
                'MAIL_EXTERNAL_API_ENABLED' => $request->external_api_enabled ? 'true' : 'false',
                'MAIL_EXTERNAL_API_URL' => $request->external_api_url,
                'MAIL_EXTERNAL_API_ACCOUNT' => $request->external_api_account,
                'MAIL_EXTERNAL_API_REPLY_TO' => $request->external_api_reply_to,
            ];

            if ($request->filled('external_api_password')) {
                $updates['MAIL_EXTERNAL_API_PASSWORD'] = $request->external_api_password;
            }

            foreach ($updates as $key => $value) {
                $pattern = "/^{$key}=.*/m";
                $replacement = "{$key}={$value}";
                
                if (preg_match($pattern, $envContent)) {
                    $envContent = preg_replace($pattern, $replacement, $envContent);
                } else {
                    $envContent .= "\n{$replacement}";
                }
            }

            File::put($envPath, $envContent);

            // 清除配置快取
            Artisan::call('config:clear');

            return back()->with('success', '郵件設定已更新');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => '更新設定失敗: ' . $e->getMessage()]);
        }
    }

    /**
     * 測試外部 API 連線
     */
    public function testApi(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        try {
            $externalMailService = new ExternalMailService();
            
            $result = $externalMailService->sendMailService(
                '外部 API 測試郵件 - ' . now()->format('Y-m-d H:i:s'),
                $this->getTestEmailContent(),
                $request->test_email
            );

            if ($result['success']) {
                return back()->with('success', '測試郵件已發送，請檢查收件匣');
            } else {
                return back()->withErrors(['test' => '測試失敗: ' . $result['message']]);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['test' => '測試失敗: ' . $e->getMessage()]);
        }
    }

    /**
     * 取得目前郵件發送統計
     */
    public function getStats()
    {
        // 這裡可以加入郵件發送統計邏輯
        return response()->json([
            'total_sent' => 0,
            'success_rate' => 0,
            'last_sent' => null,
        ]);
    }

    /**
     * 取得測試郵件內容
     */
    private function getTestEmailContent(): string
    {
        return '
        <!DOCTYPE html>
        <html lang="zh-TW">
        <head>
            <meta charset="UTF-8">
            <title>外部 API 測試郵件</title>
            <style>
                body { font-family: "Microsoft JhengHei", Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
                .content { background: #fff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; margin-top: 20px; }
                .success { color: #28a745; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧪 外部郵件 API 測試</h1>
                </div>
                <div class="content">
                    <p class="success">✅ 外部郵件 API 連線測試成功！</p>
                    <p>測試時間：' . now()->format('Y-m-d H:i:s') . '</p>
                    <p>如果您收到這封郵件，表示外部 API 設定正確且可正常發送郵件。</p>
                </div>
            </div>
        </body>
        </html>';
    }
}
