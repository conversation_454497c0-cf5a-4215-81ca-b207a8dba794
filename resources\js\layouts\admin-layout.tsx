import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppSidebarHeader } from '@/components/app-sidebar-header';
import { FixedLanguageSwitcher } from '@/components/fixed-language-switcher';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';

interface AdminLayoutProps {
    children: React.ReactNode;
    breadcrumbs?: BreadcrumbItem[];
    title?: string;
    description?: string;
}

/**
 * 管理員專用 Layout
 * 
 * 提供統一的側邊欄導航和頁面結構給所有管理員功能頁面使用
 * 包含：
 * - 統一的側邊欄導航
 * - 麵包屑導航
 * - 語言切換器
 * - 響應式設計
 */
export default function AdminLayout({ 
    children, 
    breadcrumbs = [], 
    title, 
    description 
}: PropsWithChildren<AdminLayoutProps>) {
    const { auth } = usePage<SharedData>().props;
    
    // 確保只有管理員可以使用此 layout
    if (auth?.user?.role !== 'admin') {
        return (
            <div className="flex min-h-screen items-center justify-center">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-900">存取被拒絕</h1>
                    <p className="mt-2 text-gray-600">您沒有權限存取此頁面</p>
                </div>
            </div>
        );
    }

    return (
        <AppShell variant="sidebar">
            <AppSidebar />
            <AppContent variant="sidebar" className="overflow-x-hidden">
                <AppSidebarHeader breadcrumbs={breadcrumbs} />
                
                {/* 頁面標題區域 */}
                {(title || description) && (
                    <div className="border-b border-gray-200 bg-white px-6 py-4">
                        <div className="flex items-center justify-between">
                            <div>
                                {title && (
                                    <h1 className="text-2xl font-bold tracking-tight text-gray-900">
                                        {title}
                                    </h1>
                                )}
                                {description && (
                                    <p className="mt-1 text-sm text-gray-600">
                                        {description}
                                    </p>
                                )}
                            </div>
                        </div>
                    </div>
                )}
                
                {/* 主要內容區域 */}
                <div className="flex-1">
                    {children}
                </div>
            </AppContent>
            <FixedLanguageSwitcher />
        </AppShell>
    );
}
