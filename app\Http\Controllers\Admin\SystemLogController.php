<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SystemLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

/**
 * 系統日誌控制器
 * 
 * 管理系統日誌的查看和管理功能
 */
class SystemLogController extends Controller
{
    /**
     * 顯示系統日誌頁面
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'nullable|string|in:login,operation,error,system',
                'level' => 'nullable|string|in:debug,info,warning,error,critical',
                'user_id' => 'nullable|integer|exists:users,id',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'search' => 'nullable|string|max:255',
                'per_page' => 'nullable|integer|min:10|max:100',
                'page' => 'nullable|integer|min:1',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator->errors());
            }

            $query = SystemLog::with('user:id,name,email,role')
                ->orderBy('created_at', 'desc');

            // 篩選條件
            if ($request->filled('type')) {
                $query->where('type', $request->input('type'));
            }

            if ($request->filled('level')) {
                $query->where('level', $request->input('level'));
            }

            if ($request->filled('user_id')) {
                $query->where('user_id', $request->input('user_id'));
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->input('date_from'));
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->input('date_to'));
            }

            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('description', 'like', "%{$search}%")
                      ->orWhere('action', 'like', "%{$search}%")
                      ->orWhere('ip_address', 'like', "%{$search}%");
                });
            }

            $perPage = $request->input('per_page', 20);
            $logs = $query->paginate($perPage);

            // 統計資訊
            $statistics = SystemLog::getLogStatistics(30);

            // 記錄查看日誌的操作
            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                '查看系統日誌',
                [
                    'filters' => $request->only(['type', 'level', 'user_id', 'date_from', 'date_to', 'search']),
                    'page' => $request->input('page', 1),
                    'per_page' => $perPage,
                ]
            );

            return Inertia::render('admin/system-logs', [
                'logs' => $logs,
                'statistics' => $statistics,
                'filters' => $request->only(['type', 'level', 'user_id', 'date_from', 'date_to', 'search']),
                'log_types' => [
                    SystemLog::TYPE_LOGIN => '登入',
                    SystemLog::TYPE_OPERATION => '操作',
                    SystemLog::TYPE_ERROR => '錯誤',
                    SystemLog::TYPE_SYSTEM => '系統',
                ],
                'log_levels' => [
                    SystemLog::LEVEL_DEBUG => '除錯',
                    SystemLog::LEVEL_INFO => '資訊',
                    SystemLog::LEVEL_WARNING => '警告',
                    SystemLog::LEVEL_ERROR => '錯誤',
                    SystemLog::LEVEL_CRITICAL => '嚴重',
                ],
            ]);

        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_VIEW,
                '查看系統日誌頁面失敗',
                $e,
                ['request_data' => $request->all()]
            );

            return back()->withErrors([
                'system' => '載入系統日誌失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 顯示單一日誌詳情
     *
     * @param int $id
     * @return \Inertia\Response
     */
    public function show(int $id)
    {
        try {
            $log = SystemLog::with('user:id,name,email,role')->findOrFail($id);

            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                "查看日誌詳情 ID: {$id}",
                ['log_id' => $id]
            );

            return Inertia::render('admin/system-log-detail', [
                'log' => $log,
            ]);

        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_VIEW,
                "查看日誌詳情失敗 ID: {$id}",
                $e
            );

            return back()->withErrors([
                'system' => '載入日誌詳情失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 清理舊日誌
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cleanup(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'days_to_keep' => 'required|integer|min:7|max:365',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator->errors());
            }

            $daysToKeep = $request->input('days_to_keep');
            $deletedCount = SystemLog::cleanupOldLogs($daysToKeep);

            SystemLog::logOperation(
                SystemLog::ACTION_DELETE,
                "清理了 {$deletedCount} 筆舊日誌記錄",
                [
                    'days_to_keep' => $daysToKeep,
                    'deleted_count' => $deletedCount,
                ]
            );

            return back()->with('success', "已成功清理 {$deletedCount} 筆舊日誌記錄");

        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_DELETE,
                '清理舊日誌失敗',
                $e,
                ['request_data' => $request->all()]
            );

            return back()->withErrors([
                'system' => '清理舊日誌失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 匯出日誌
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'nullable|string|in:login,operation,error,system',
                'level' => 'nullable|string|in:debug,info,warning,error,critical',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'format' => 'required|string|in:csv,json',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator->errors());
            }

            $query = SystemLog::with('user:id,name,email')
                ->orderBy('created_at', 'desc');

            // 應用篩選條件
            if ($request->filled('type')) {
                $query->where('type', $request->input('type'));
            }

            if ($request->filled('level')) {
                $query->where('level', $request->input('level'));
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->input('date_from'));
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->input('date_to'));
            }

            $logs = $query->get();
            $format = $request->input('format', 'csv');
            $filename = 'system_logs_' . now()->format('Y-m-d_H-i-s') . '.' . $format;

            SystemLog::logOperation(
                SystemLog::ACTION_DOWNLOAD,
                "匯出系統日誌 ({$format} 格式)",
                [
                    'format' => $format,
                    'total_records' => $logs->count(),
                    'filters' => $request->only(['type', 'level', 'date_from', 'date_to']),
                ]
            );

            if ($format === 'csv') {
                return $this->exportCsv($logs, $filename);
            } else {
                return $this->exportJson($logs, $filename);
            }

        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_DOWNLOAD,
                '匯出系統日誌失敗',
                $e,
                ['request_data' => $request->all()]
            );

            return back()->withErrors([
                'system' => '匯出系統日誌失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 匯出 CSV 格式
     *
     * @param \Illuminate\Database\Eloquent\Collection $logs
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    private function exportCsv($logs, string $filename)
    {
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        return response()->stream(function () use ($logs) {
            $handle = fopen('php://output', 'w');
            
            // 加入 BOM 以支援中文
            fwrite($handle, "\xEF\xBB\xBF");
            
            // CSV 標題
            fputcsv($handle, [
                'ID', '類型', '動作', '描述', '用戶', '用戶角色', 
                'IP地址', '級別', '建立時間'
            ]);

            // CSV 資料
            foreach ($logs as $log) {
                fputcsv($handle, [
                    $log->id,
                    $log->type,
                    $log->action,
                    $log->description,
                    $log->user ? $log->user->name : '系統',
                    $log->user ? $log->user->role : '',
                    $log->ip_address,
                    $log->level,
                    $log->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($handle);
        }, 200, $headers);
    }

    /**
     * 匯出 JSON 格式
     *
     * @param \Illuminate\Database\Eloquent\Collection $logs
     * @param string $filename
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    private function exportJson($logs, string $filename)
    {
        $headers = [
            'Content-Type' => 'application/json; charset=UTF-8',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        return response()->stream(function () use ($logs) {
            echo json_encode($logs->toArray(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }, 200, $headers);
    }
}
