import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Filter, Download, Eye, Mail, Trash2, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';
import DataTable from '@/components/admin/DataTable';

interface EmailLog {
    id: number;
    recommendation_letter_id?: number;
    recipient_email: string;
    recipient_name?: string;
    email_type: string;
    subject: string;
    content?: string;
    status: string;
    sent_at?: string;
    error_message?: string;
    retry_count?: number;
    created_at: string;
    recommendationLetter?: {
        id: number;
        department_name: string;
        program_type: string;
    };
}

interface EmailLogsProps {
    emailLogs: {
        data: EmailLog[];
        current_page: number;
        last_page: number;
        from: number;
        to: number;
        total: number;
    };
}

export default function EmailLogs({ emailLogs }: EmailLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: 'Email 日誌', href: '/admin/email-logs' },
    ];

    // 狀態徽章
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'sent':
                return (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        已發送
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                        <XCircle className="mr-1 h-3 w-3" />
                        發送失敗
                    </Badge>
                );
            case 'pending':
                return (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        <Clock className="mr-1 h-3 w-3" />
                        待發送
                    </Badge>
                );
            case 'bounced':
                return (
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                        <AlertCircle className="mr-1 h-3 w-3" />
                        退信
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    // Email 類型徽章
    const getTypeBadge = (type: string) => {
        switch (type) {
            case 'invitation':
                return (
                    <Badge variant="outline" className="text-blue-700">
                        邀請信
                    </Badge>
                );
            case 'reminder':
                return (
                    <Badge variant="outline" className="text-orange-700">
                        提醒信
                    </Badge>
                );
            case 'submitted':
                return (
                    <Badge variant="outline" className="text-green-700">
                        完成通知
                    </Badge>
                );
            case 'declined':
                return (
                    <Badge variant="outline" className="text-red-700">
                        婉拒通知
                    </Badge>
                );
            case 'notification':
                return (
                    <Badge variant="outline" className="text-purple-700">
                        系統通知
                    </Badge>
                );
            default:
                return <Badge variant="outline">{type}</Badge>;
        }
    };

    // 格式化日期
    const formatDate = (dateString?: string) => {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString('zh-TW');
    };

    // 查看詳情
    const handleViewDetail = (log: EmailLog) => {
        router.visit(route('admin.email-logs.show', log.id));
    };

    // 匯出 Email 日誌
    const handleExportLogs = () => {
        const params = new URLSearchParams();
        if (statusFilter !== 'all') params.append('status', statusFilter);
        if (typeFilter !== 'all') params.append('type', typeFilter);
        if (searchTerm) params.append('search', searchTerm);

        window.open(`/admin/email-logs/export?${params.toString()}`, '_blank');
    };

    // 清理舊日誌
    const handleCleanupLogs = () => {
        if (confirm('確定要清理 30 天前的舊日誌嗎？此操作無法復原。')) {
            router.post(
                '/admin/email-logs/cleanup',
                { days: 30 },
                {
                    onSuccess: () => {
                        alert('舊日誌已清理完成');
                        router.reload();
                    },
                    onError: () => {
                        alert('清理失敗，請重試');
                    },
                },
            );
        }
    };

    // 定義表格欄位
    const columns = [
        {
            key: 'sent_at',
            label: '發送時間',
            sortable: true,
            render: (value: string, row: EmailLog) => <div className="text-sm">{formatDate(value || row.created_at)}</div>,
        },
        {
            key: 'recipient_email',
            label: '收件人',
            sortable: true,
            render: (value: string, row: EmailLog) => (
                <div>
                    <div className="font-medium">{value}</div>
                    {row.recipient_name && <div className="text-sm text-gray-500">{row.recipient_name}</div>}
                </div>
            ),
        },
        {
            key: 'subject',
            label: '主旨',
            render: (value: string) => (
                <div className="max-w-xs truncate" title={value}>
                    {value}
                </div>
            ),
        },
        {
            key: 'email_type',
            label: '類型',
            render: (value: string) => getTypeBadge(value),
        },
        {
            key: 'status',
            label: '狀態',
            render: (value: string, row: EmailLog) => <div>{getStatusBadge(value)}</div>,
        },
        {
            key: 'recommendationLetter',
            label: '相關推薦函',
            render: (value: any) => {
                if (!value) return <span className="text-gray-400">N/A</span>;
                return (
                    <div className="text-sm">
                        <div>{value.department_name}</div>
                        <div className="text-gray-500">{value.program_type}</div>
                    </div>
                );
            },
        },
    ];

    // 定義操作按鈕
    // todo 建立詳細頁面
    const actions = [
        {
            key: 'view',
            label: '查看',
            icon: <Eye className="h-3 w-3" />,
            onClick: handleViewDetail,
            variant: 'outline' as const,
        },
    ];

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="Email 日誌" description="查看和管理系統發送的所有 Email 記錄">
            <Head title="Email 日誌" />

            <div className="space-y-6 p-6">
                {/* 搜尋和過濾 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Mail className="h-5 w-5" />
                            搜尋和過濾
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-4">
                            <div className="min-w-64 flex-1">
                                <Input placeholder="搜尋收件人、主旨或系所..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                            </div>
                            <div className="min-w-32">
                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="狀態" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">所有狀態</SelectItem>
                                        <SelectItem value="sent">已發送</SelectItem>
                                        <SelectItem value="failed">發送失敗</SelectItem>
                                        <SelectItem value="pending">待發送</SelectItem>
                                        <SelectItem value="bounced">退信</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="min-w-32">
                                <Select value={typeFilter} onValueChange={setTypeFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="類型" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">所有類型</SelectItem>
                                        <SelectItem value="invitation">邀請信</SelectItem>
                                        <SelectItem value="reminder">提醒信</SelectItem>
                                        <SelectItem value="submitted">完成通知</SelectItem>
                                        <SelectItem value="declined">婉拒通知</SelectItem>
                                        <SelectItem value="notification">系統通知</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <Button variant="outline" onClick={handleExportLogs}>
                                <Download className="mr-2 h-4 w-4" />
                                匯出
                            </Button>
                            <Button variant="outline" onClick={handleCleanupLogs}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                清理
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* 統計資訊 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Mail className="h-8 w-8 text-blue-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">總計</p>
                                    <p className="text-2xl font-bold text-gray-900">{emailLogs.total}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <CheckCircle className="h-8 w-8 text-green-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">已發送</p>
                                    <p className="text-2xl font-bold text-gray-900">{emailLogs.data.filter((log) => log.status === 'sent').length}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <XCircle className="h-8 w-8 text-red-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">失敗</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {emailLogs.data.filter((log) => log.status === 'failed').length}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Clock className="h-8 w-8 text-yellow-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">待發送</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {emailLogs.data.filter((log) => log.status === 'pending').length}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* 資料表格 */}
                <DataTable
                    data={emailLogs.data.filter((log) => {
                        const matchesSearch =
                            log.recipient_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            log.subject?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            log.recommendationLetter?.department_name?.toLowerCase().includes(searchTerm.toLowerCase());

                        const matchesStatus = statusFilter === 'all' || log.status === statusFilter;
                        const matchesType = typeFilter === 'all' || log.email_type === typeFilter;

                        return matchesSearch && matchesStatus && matchesType;
                    })}
                    columns={columns}
                    actions={actions}
                    title="Email 日誌列表"
                    description={`顯示第 ${emailLogs.from} 到 ${emailLogs.to} 筆，共 ${emailLogs.total} 筆記錄`}
                    emptyMessage="沒有找到符合條件的 Email 記錄"
                    exportable={true}
                    onExport={handleExportLogs}
                />
            </div>
        </AdminLayout>
    );
}
