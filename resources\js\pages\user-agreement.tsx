import { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { User } from '@/types';
import AppLogoIcon from '@/components/app-logo-icon';

interface Props {
    user: User;
    userRole: string;
    errors?: {
        agree?: string;
    };
}

/**
 * 考生與推薦人共同使用的個人資料使用條款頁面。
 *
 * @param user - 當前使用者的資料
 * @param userRole - 使用者角色（考生或推薦人）
 * @param errors - 可能的錯誤訊息
 * @returns
 */
export default function UserAgreement({ user, userRole, errors }: Props) {
    const [agreed, setAgreed] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [scrolledToBottom, setScrolledToBottom] = useState(false);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!agreed) {
            return;
        }

        setIsSubmitting(true);

        router.post(
            '/user-agreement',
            {
                agree: agreed,
            },
            {
                onFinish: () => setIsSubmitting(false),
            },
        );
    };

    return (
        <>
            <Head title="個人資料使用條款" />

            <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
                <div className="w-full max-w-2xl space-y-8">
                    <Card>
                        <CardHeader className="space-y-2 text-center">
                            <div className="flex justify-center">
                                <AppLogoIcon className="h-10 w-10" />
                            </div>
                            <CardTitle className="text-2xl">個人資料保護聲明</CardTitle>
                            <CardDescription className="text-sm">請仔細閱讀以下條款，並確認您同意我們的個人資料使用方式。</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2 rounded-lg bg-gray-50 p-4">
                                <div>
                                    <span className="font-medium">姓名：</span>
                                    <span>{user.name}</span>
                                </div>
                                <div>
                                    <span className="font-medium">電子信箱：</span>
                                    <span>{user.email}</span>
                                </div>
                                <div>
                                    <span className="font-medium">聯絡方式：</span>
                                    <span>{user.phone || '未提供'}</span>
                                </div>
                            </div>
                            {userRole === 'recommender' && (
                                <div className="text-sm leading-relaxed text-red-600">
                                    推薦人資料為考生輸入，若有錯誤，請於登入後進行更新，或聯繫系統管理員協助修改。
                                </div>
                            )}

                            {/* 個人資料使用聲明 */}
                            <div
                                className="max-h-64 overflow-y-auto rounded-lg bg-blue-50 p-4 text-sm"
                                onScroll={(e) => {
                                    const el = e.currentTarget;
                                    const isBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 10;
                                    setScrolledToBottom(isBottom);
                                }}
                            >
                                <h4 className="font-semibold text-blue-900">資料蒐集目的：</h4>
                                <ul className="list-inside list-disc space-y-1 text-blue-800">
                                    <li>推薦函申請與管理作業</li>
                                    <li>身份驗證與系統安全維護</li>
                                    <li>相關通知與聯繫事宜</li>
                                </ul>

                                <h4 className="mt-4 font-semibold text-blue-900">資料使用範圍：</h4>
                                <ul className="list-inside list-disc space-y-1 text-blue-800">
                                    <li>僅限於推薦函系統相關業務使用</li>
                                    <li>不會將您的個人資料提供給第三方</li>
                                    <li>資料將依法妥善保存與管理</li>
                                </ul>

                                <h4 className="mt-4 font-semibold text-blue-900">您的權利：</h4>
                                <ul className="list-inside list-disc space-y-1 text-blue-800">
                                    <li>查詢、更正或刪除個人資料</li>
                                    <li>停止蒐集、處理或利用個人資料</li>
                                    <li>如有疑問可隨時聯繫系統管理員</li>
                                </ul>
                            </div>

                            {errors?.agree && (
                                <Alert variant="destructive">
                                    <AlertDescription>{errors.agree}</AlertDescription>
                                </Alert>
                            )}

                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="agree"
                                        checked={agreed}
                                        disabled={!scrolledToBottom}
                                        onCheckedChange={(checked) => setAgreed(checked as boolean)}
                                    />
                                    <label
                                        htmlFor="agree"
                                        className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                    >
                                        我已詳細閱讀並同意上述個人資料使用條款
                                    </label>
                                </div>

                                <div className="flex justify-end space-x-3">
                                    <Button type="button" variant="outline" onClick={() => router.post('/logout')}>
                                        不同意並登出
                                    </Button>
                                    <Button type="submit" disabled={!agreed || isSubmitting}>
                                        {isSubmitting ? '處理中...' : '同意並繼續'}
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
