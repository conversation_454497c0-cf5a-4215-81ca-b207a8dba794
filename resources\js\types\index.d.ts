import { LucideIcon } from 'lucide-react';
import type { Config } from 'ziggy-js';

export interface Auth {
    user: User;
}

export interface BreadcrumbItem {
    title: string;
    href: string;
}

export interface NavGroup {
    title: string;
    items: NavItem[];
}

export interface NavItem {
    title: string;
    href: string;
    icon?: LucideIcon | null;
    isActive?: boolean;
}

export interface SharedData {
    name: string;
    quote: { message: string; author: string };
    auth: Auth;
    ziggy: Config & { location: string };
    sidebarOpen: boolean;
    recommendations: Array<{
        id: number;
        applicant_id: number;
        external_autono: string;
        department_name: string;
        program_type: string;
        recommender_department: string;
        status: string;
        recommender_email?: string;
        recommender_name?: string;
        recommender_title?: string;
        recommender_phone?: string;
        pdf_path?: string;
        submitted_at?: string;
        created_at?: string;
        updated_at?: string;
        applicant?: {
            id: number;
            user_id: number;
            user?: {
                id: number;
                name: string;
                email: string;
                role: string;
            };
        };
        recommender?: any;
    }>;
    applications?: Array<{
        autono: number;
        dep_no: string;
        exam_name: string;
        dep_name: string;
    }>;
    admission_info?: Array<{
        exam_id: string;
        exam_name: string;
        app_date1_start: string;
        app_date1_end: string;
    }>;
    applicant_id?: number;
    applicant_info: {
        user: {
            id: number;
            name: string;
            email: string;
            phone?: string;
        };
    };
    user_role?: string;
    submission_settings?: {
        allow_pdf_upload: boolean;
        allow_questionnaire_submission: boolean;
    };
    questionnaire_templates?: Record<string, QuestionnaireTemplate>;
    recommender?: any;
    [key: string]: unknown;
}

export interface User {
    id: number;
    name: string;
    email: string;
    phone?: string; // Optional, as not all users may have a phone number
    created_at: string;
    updated_at: string;
    role: string; // Assuming role is a string, adjust as necessary
    [key: string]: unknown; // This allows for additional properties...
}

interface QuestionnaireTemplate {
    id: number;
    department_name: string;
    program_type: string;
    template_name: string;
    description: string;
    questions: any;
    is_active: boolean;
    sort_order: number;
    created_at: string;
    updated_at: string;
}


interface AdminRecommendation {
    id: number;
    applicant_id: number;
    external_autono: string;
    department_name: string;
    program_type: string;
    recommender_department: string;
    status: string;
    recommender_email?: string;
    recommender_name?: string;
    submitted_at?: string;
    created_at?: string;
    pdf_path?: string;
    applicant?: {
        user?: {
            name: string;
            email: string;
        };
    };
}

interface SystemSetting {
    id: number;
    key: string;
    value: string;
    type: string;
    category: string;
    description: string;
    is_active: boolean;
}

interface SystemLog {
    id: number;
    type: string;
    action: string;
    description: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
    ip_address: string;
    level: string;
    created_at: string;
}

export interface EmailLog {
    id: number;
    recommendation_letter_id?: number;
    recipient_email: string;
    recipient_name?: string;
    email_type: string;
    subject: string;
    content?: string;
    status: string;
    sent_at?: string;
    error_message?: string;
    created_at: string;
    recommendation_letter?: {
        id: number;
        department_name: string;
        program_type: string;
    };
}

export interface LoginLog {
    id: number;
    user_id?: number;
    ip_address: string;
    user_agent: string;
    success: boolean;
    failure_reason?: string;
    login_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

export interface DashboardAdminProps {
    recommendations: AdminRecommendation[];
    templates?: QuestionnaireTemplate[];
    settings?: {
        timing: SystemSetting[];
        general: SystemSetting[];
        security: SystemSetting[];
        email: SystemSetting[];
    };
    logs?: {
        data: SystemLog[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    emailLogs?: {
        data: EmailLog[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    loginLogs?: {
        data: LoginLog[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
}
