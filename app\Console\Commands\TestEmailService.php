<?php

namespace App\Console\Commands;

use App\Services\EmailService;
use App\Services\ExternalMailService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 測試郵件服務的 Artisan 命令
 * 
 * php artisan emails:test <EMAIL>
 * php artisan emails:test <EMAIL> --type=connection
 */
class TestEmailService extends Command
{
    /**
     * The name and signature of the console command.
     * 
     * @var string
     */
    protected $signature = 'emails:test 
                            {email : 測試郵件的收件人信箱}
                            {--type=api : 測試類型 (api|connection)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '測試郵件服務功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $type = $this->option('type');

        $this->info("開始測試郵件服務 (類型: {$type})...");

        try {
            if ($type === 'connection') {
                return $this->testConnection($email);
            } else {
                return $this->testApiService($email);
            }
        } catch (\Exception $e) {
            $this->error('測試失敗: ' . $e->getMessage());

            Log::error('郵件服務測試失敗', [
                'email' => $email,
                'type' => $type,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * 測試外部API連線
     */
    private function testConnection(string $email): int
    {
        $this->info('測試外部API連線...');

        $externalMailService = new ExternalMailService();
        $result = $externalMailService->testConnection();

        if ($result['success']) {
            $this->info('✅ 外部API連線測試成功');
            $this->line('API回應: ' . ($result['api_response'] ?? 'N/A'));
        } else {
            $this->error('❌ 外部API連線測試失敗');
            $this->line('錯誤訊息: ' . $result['message']);
        }

        return $result['success'] ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * 測試API服務
     */
    private function testApiService(string $email): int
    {
        $this->info('測試API服務...');

        $externalMailService = new ExternalMailService();

        $testContent = $this->getTestEmailContent();
        $result = $externalMailService->sendMailService(
            '郵件服務測試 Email Service Test - ' . now()->format('Y-m-d H:i:s'),
            $testContent,
            $email
        );

        if ($result['success']) {
            $this->info('✅ 測試郵件發送成功');
            $this->line('收件人: ' . $email);
            $this->line('API回應: ' . ($result['api_response'] ?? 'N/A'));
        } else {
            $this->error('❌ 測試郵件發送失敗');
            $this->line('錯誤訊息: ' . $result['message']);
        }

        return $result['success'] ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * 取得測試郵件內容
     */
    private function getTestEmailContent(): string
    {
        return '
        <html>
        <head>
            <meta charset="UTF-8">
            <title>郵件服務測試 Email Service Test</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h1 style="color: #007bff;">郵件服務測試 Email Service Test</h1>
                
                <p>親愛的用戶，您好：</p>
                <p>Dear User,</p>
                
                <p>這是一封測試郵件，用於驗證推薦函系統的郵件服務功能。</p>
                <p>This is a test email to verify the email service functionality of the recommendation system.</p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3>測試資訊 Test Information</h3>
                    <ul>
                        <li><strong>發送時間 Sent at:</strong> ' . now()->format('Y-m-d H:i:s') . '</li>
                        <li><strong>系統版本 System Version:</strong> Laravel ' . app()->version() . '</li>
                        <li><strong>郵件服務 Email Service:</strong> External API</li>
                    </ul>
                </div>
                
                <p>如果您收到此郵件，表示郵件服務運作正常。</p>
                <p>If you receive this email, it means the email service is working properly.</p>
                
                <hr style="margin: 30px 0;">
                <p style="font-size: 12px; color: #666;">
                    此郵件由推薦函管理系統自動發送<br>
                    This email is automatically sent by the recommendation letter management system
                </p>
            </div>
        </body>
        </html>';
    }
}
