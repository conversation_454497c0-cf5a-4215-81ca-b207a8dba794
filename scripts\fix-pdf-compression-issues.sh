#!/bin/bash

# PDF 壓縮系統問題修復腳本

echo "=== PDF 壓縮系統問題修復腳本 ==="

# 獲取項目根目錄
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))
echo "項目根目錄: $PROJECT_ROOT"

# 檢查是否為 Laravel 項目
if [ ! -f "$PROJECT_ROOT/artisan" ]; then
    echo "錯誤: 這不是一個 Laravel 項目目錄"
    exit 1
fi

cd "$PROJECT_ROOT"

echo ""
echo "=== 步驟 1: 檢查和修復存儲目錄 ==="

# 創建必要的目錄
echo "創建必要的目錄..."
mkdir -p storage/app/public/pdf_merges
mkdir -p storage/app/private/recommendations
mkdir -p storage/logs
mkdir -p storage/framework/{cache,sessions,views}
mkdir -p bootstrap/cache

# 設置權限
echo "設置目錄權限..."
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/

# 特別設置 PDF 目錄權限
chmod -R 755 storage/app/public/
find storage/app/public/pdf_merges/ -type f -exec chmod 644 {} \; 2>/dev/null || true

echo "✓ 存儲目錄權限已修復"

echo ""
echo "=== 步驟 2: 檢查和創建 Storage Link ==="

if [ -L "public/storage" ]; then
    echo "Storage link 已存在，檢查是否有效..."
    if [ -d "$(readlink public/storage)" ]; then
        echo "✓ Storage link 有效"
    else
        echo "Storage link 無效，重新創建..."
        rm public/storage
        php artisan storage:link
    fi
else
    echo "創建 storage link..."
    php artisan storage:link
fi

echo ""
echo "=== 步驟 3: 檢查 ZIP 擴展 ==="

if php -m | grep -q zip; then
    echo "✓ ZIP 擴展已安裝"
else
    echo "✗ ZIP 擴展未安裝"
    echo "請安裝 PHP ZIP 擴展:"
    echo "  Ubuntu/Debian: sudo apt-get install php-zip"
    echo "  CentOS/RHEL: sudo yum install php-zip"
    echo "  macOS: brew install php-zip"
fi

echo ""
echo "=== 步驟 4: 檢查磁碟空間 ==="

AVAILABLE_SPACE=$(df -BG . | tail -1 | awk '{print $4}' | sed 's/G//')
echo "可用磁碟空間: ${AVAILABLE_SPACE}GB"

if [ "$AVAILABLE_SPACE" -lt 1 ]; then
    echo "⚠️  警告: 磁碟空間不足，建議至少保留 1GB 空間"
else
    echo "✓ 磁碟空間充足"
fi

echo ""
echo "=== 步驟 5: 測試檔案創建 ==="

TEST_FILE="storage/app/public/pdf_merges/test_$(date +%s).txt"
echo "測試內容" > "$TEST_FILE"

if [ -f "$TEST_FILE" ]; then
    echo "✓ 檔案創建測試成功"
    rm "$TEST_FILE"
else
    echo "✗ 檔案創建測試失敗"
    echo "請檢查目錄權限和磁碟空間"
fi

echo ""
echo "=== 步驟 6: 檢查 Web 服務器配置 ==="

# 檢查是否可以訪問 storage 目錄
if [ -f "public/storage/.gitignore" ] || [ -d "public/storage" ]; then
    echo "✓ Storage 目錄可通過 Web 訪問"
else
    echo "⚠️  警告: Storage 目錄可能無法通過 Web 訪問"
    echo "請確保 Web 服務器配置正確"
fi

echo ""
echo "=== 步驟 7: 清理舊檔案（可選） ==="

OLD_FILES=$(find storage/app/public/pdf_merges/ -name "*.zip" -mtime +7 2>/dev/null | wc -l)
if [ "$OLD_FILES" -gt 0 ]; then
    echo "發現 $OLD_FILES 個超過 7 天的舊 ZIP 檔案"
    read -p "是否要清理這些檔案? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        find storage/app/public/pdf_merges/ -name "*.zip" -mtime +7 -delete 2>/dev/null
        echo "✓ 舊檔案已清理"
    fi
else
    echo "✓ 沒有需要清理的舊檔案"
fi

echo ""
echo "=== 步驟 8: 執行測試 ==="

echo "執行系統測試..."
if php artisan pdf:test-compression --list-available >/dev/null 2>&1; then
    echo "✓ 測試指令可正常執行"
    echo ""
    echo "可用的測試選項:"
    php artisan pdf:test-compression --list-available
else
    echo "✗ 測試指令執行失敗"
    echo "請檢查系統配置和資料庫連接"
fi

echo ""
echo "=== 修復完成 ==="
echo ""
echo "修復摘要:"
echo "✓ 存儲目錄權限已設置"
echo "✓ Storage link 已創建/檢查"
echo "✓ 檔案創建權限已測試"
echo ""
echo "建議的後續步驟:"
echo "1. 執行完整測試: php artisan pdf:test-compression --exam_id=2 --exam_year=114 --dry-run"
echo "2. 檢查 Web 服務器日誌是否有錯誤"
echo "3. 測試外部下載: curl -I https://your-domain.com/api/public/pdf-download/test"
echo ""
echo "如果問題仍然存在，請檢查:"
echo "- Web 服務器配置 (Apache/Nginx)"
echo "- PHP 配置 (memory_limit, max_execution_time)"
echo "- 防火牆設置"
echo "- SELinux 設置 (如果適用)"
