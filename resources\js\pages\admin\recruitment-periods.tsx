import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
    Calendar, 
    RefreshCw, 
    Download, 
    Clock, 
    CheckCircle, 
    XCircle, 
    AlertTriangle,
    Database,
    Activity
} from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';
import DataTable from '@/components/admin/DataTable';
import StatsCards from '@/components/admin/StatsCards';

interface PeriodInfo {
    exists: boolean;
    exam_id: string;
    exam_name?: string;
    start_time?: string;
    end_time?: string;
    status: 'not_started' | 'active' | 'ended' | 'unknown';
    is_active: boolean;
    days_until_start?: number;
    days_until_end?: number;
    synced_at?: string;
    error?: string;
}

interface RecruitmentPeriodsProps {
    periods: PeriodInfo[];
    last_sync_time: string | null;
    needs_resync: boolean;
    active_periods_count: number;
    breadcrumbs: { title: string; href: string }[];
}

export default function RecruitmentPeriods({ 
    periods, 
    last_sync_time, 
    needs_resync, 
    active_periods_count,
    breadcrumbs 
}: RecruitmentPeriodsProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState('overview');

    // 處理同步
    const handleSync = () => {
        setIsLoading(true);
        router.post('/admin/recruitment-periods/sync', {}, {
            onFinish: () => setIsLoading(false)
        });
    };

    // 處理清除快取
    const handleClearCache = () => {
        setIsLoading(true);
        router.post('/admin/recruitment-periods/clear-cache', {}, {
            onFinish: () => setIsLoading(false)
        });
    };

    // 處理匯出
    const handleExport = () => {
        window.location.href = '/admin/recruitment-periods/export';
    };

    // 獲取狀態徽章
    const getStatusBadge = (status: string, isActive: boolean) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-100 text-green-800">進行中</Badge>;
            case 'not_started':
                return <Badge className="bg-blue-100 text-blue-800">未開始</Badge>;
            case 'ended':
                return <Badge className="bg-gray-100 text-gray-800">已結束</Badge>;
            default:
                return <Badge className="bg-red-100 text-red-800">狀態異常</Badge>;
        }
    };

    // 格式化時間
    const formatDateTime = (dateTime: string | undefined) => {
        if (!dateTime) return '未知';
        return new Date(dateTime).toLocaleString('zh-TW');
    };

    // 統計資料
    const stats = [
        {
            title: '總招生期間',
            value: periods.length,
            icon: Calendar,
            color: 'default' as const
        },
        {
            title: '進行中',
            value: active_periods_count,
            icon: CheckCircle,
            color: 'green' as const
        },
        {
            title: '已結束',
            value: periods.filter(p => p.status === 'ended').length,
            icon: XCircle,
            color: 'gray' as const
        },
        {
            title: '需要同步',
            value: needs_resync ? '是' : '否',
            icon: needs_resync ? AlertTriangle : CheckCircle,
            color: needs_resync ? 'yellow' : 'green' as const
        }
    ];

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="招生期間管理" description="管理和監控招生期間資料">
            <Head title="招生期間管理" />

            <div className="space-y-6 p-6">
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="overview">總覽</TabsTrigger>
                        <TabsTrigger value="periods">期間列表</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="space-y-6 pt-4">
                        {/* 統計卡片 */}
                        <StatsCards stats={stats} />

                        {/* 同步狀態 */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Database className="h-5 w-5" />
                                    資料同步狀態
                                </CardTitle>
                                <CardDescription>
                                    管理招生期間資料的同步和快取
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                        <div>
                                            <p className="text-sm font-medium">最後同步時間</p>
                                            <p className="text-sm text-gray-500">
                                                {last_sync_time ? formatDateTime(last_sync_time) : '從未同步'}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium">同步狀態</p>
                                            <div className="flex items-center gap-2">
                                                {needs_resync ? (
                                                    <>
                                                        <AlertTriangle className="h-4 w-4 text-yellow-500" />
                                                        <span className="text-sm text-yellow-600">需要重新同步</span>
                                                    </>
                                                ) : (
                                                    <>
                                                        <CheckCircle className="h-4 w-4 text-green-500" />
                                                        <span className="text-sm text-green-600">資料最新</span>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div className="flex gap-2">
                                        <Button 
                                            onClick={handleSync} 
                                            disabled={isLoading}
                                            className="flex items-center gap-2"
                                        >
                                            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                                            {isLoading ? '同步中...' : '立即同步'}
                                        </Button>
                                        
                                        <Button 
                                            variant="outline" 
                                            onClick={handleClearCache}
                                            disabled={isLoading}
                                        >
                                            清除快取
                                        </Button>
                                        
                                        <Button 
                                            variant="outline" 
                                            onClick={handleExport}
                                            className="flex items-center gap-2"
                                        >
                                            <Download className="h-4 w-4" />
                                            匯出資料
                                        </Button>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* 進行中的招生期間 */}
                        {active_periods_count > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Activity className="h-5 w-5" />
                                        進行中的招生期間
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        {periods
                                            .filter(period => period.is_active)
                                            .map((period) => (
                                                <div key={period.exam_id} className="flex items-center justify-between rounded-lg border p-3">
                                                    <div>
                                                        <p className="font-medium">{period.exam_name || period.exam_id}</p>
                                                        <p className="text-sm text-gray-500">
                                                            {formatDateTime(period.start_time)} - {formatDateTime(period.end_time)}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        {getStatusBadge(period.status, period.is_active)}
                                                        {period.days_until_end && (
                                                            <span className="text-sm text-gray-500">
                                                                剩餘 {period.days_until_end} 天
                                                            </span>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </TabsContent>

                    <TabsContent value="periods" className="space-y-6 pt-4">
                        {/* 招生期間列表 */}
                        <DataTable
                            data={periods}
                            title="招生期間列表"
                            description={`共 ${periods.length} 個招生期間`}
                            emptyMessage="沒有招生期間資料"
                            columns={[
                                {
                                    key: 'exam_id',
                                    label: '考試ID',
                                    sortable: true
                                },
                                {
                                    key: 'exam_name',
                                    label: '考試名稱',
                                    sortable: true,
                                    render: (value, row) => value || row.exam_id
                                },
                                {
                                    key: 'start_time',
                                    label: '開始時間',
                                    sortable: true,
                                    render: (value) => formatDateTime(value)
                                },
                                {
                                    key: 'end_time',
                                    label: '結束時間',
                                    sortable: true,
                                    render: (value) => formatDateTime(value)
                                },
                                {
                                    key: 'status',
                                    label: '狀態',
                                    sortable: true,
                                    render: (value, row) => getStatusBadge(value, row.is_active)
                                },
                                {
                                    key: 'synced_at',
                                    label: '同步時間',
                                    sortable: true,
                                    render: (value) => formatDateTime(value)
                                }
                            ]}
                            exportable={true}
                            onExport={handleExport}
                        />
                    </TabsContent>
                </Tabs>
            </div>
        </AdminLayout>
    );
}
