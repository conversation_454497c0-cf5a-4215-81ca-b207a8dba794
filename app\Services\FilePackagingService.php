<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

/**
 * 檔案打包服務
 * 
 * 負責將合併後的PDF檔案打包成ZIP檔案供下載
 */
class FilePackagingService
{
    /**
     * 創建ZIP打包檔案
     *
     * @param array $files 檔案列表
     * @param string $taskId 任務ID
     * @param array $metadata 額外的元數據
     * @return string ZIP檔案路徑
     */
    public function createZipPackage(array $files, string $taskId, array $metadata = []): string
    {
        try {
            if (empty($files)) {
                throw new \Exception('沒有提供要打包的檔案');
            }

            // 生成ZIP檔案名稱和路徑
            $zipFileName = $this->generateZipFileName($taskId, $metadata);
            $zipFilePath = 'pdf_merges/' . $zipFileName;
            $fullZipPath = storage_path('app/' . $zipFilePath);

            // 確保目錄存在
            $this->ensureDirectoryExists(dirname($fullZipPath));

            // 創建ZIP檔案
            $zip = new ZipArchive();
            $result = $zip->open($fullZipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

            if ($result !== TRUE) {
                throw new \Exception("無法創建ZIP檔案: {$result}");
            }

            // 添加檔案到ZIP
            $addedFiles = 0;
            foreach ($files as $file) {
                if ($this->addFileToZip($zip, $file)) {
                    $addedFiles++;
                }
            }

            // 添加說明檔案
            $this->addReadmeToZip($zip, $metadata, $addedFiles);

            $zip->close();

            if ($addedFiles === 0) {
                throw new \Exception('沒有成功添加任何檔案到ZIP');
            }

            Log::info('ZIP檔案創建成功', [
                'zip_path' => $zipFilePath,
                'added_files' => $addedFiles,
                'total_files' => count($files),
                'task_id' => $taskId
            ]);

            return $zipFilePath;
        } catch (\Exception $e) {
            Log::error('創建ZIP檔案失敗', [
                'error' => $e->getMessage(),
                'task_id' => $taskId,
                'file_count' => count($files)
            ]);

            throw new \Exception('創建ZIP檔案失敗: ' . $e->getMessage());
        }
    }

    /**
     * 生成ZIP檔案名稱
     *
     * @param string $taskId
     * @param array $metadata
     * @return string
     */
    protected function generateZipFileName(string $taskId, array $metadata): string
    {
        $timestamp = date('Y-m-d_H-i-s');
        $prefix = 'merged_recommendations';

        // 如果有特定的考試或系所資訊，加入檔案名稱
        if (isset($metadata['exam_id'])) {
            $prefix .= '_exam_' . $metadata['exam_id'];
        }

        if (isset($metadata['exam_year'])) {
            $prefix .= '_year_' . $metadata['exam_year'];
        }

        return "{$prefix}_{$timestamp}_{$taskId}.zip";
    }

    /**
     * 添加檔案到ZIP
     *
     * @param ZipArchive $zip
     * @param array $file
     * @return bool
     */
    protected function addFileToZip(ZipArchive $zip, array $file): bool
    {
        try {
            $filePath = $file['file_path'] ?? null;
            $fileName = $file['file_name'] ?? null;
            $applicantId = $file['applicant_id'] ?? 'unknown';

            if (!$filePath || !file_exists($filePath)) {
                Log::warning('檔案不存在，跳過添加到ZIP', ['file_path' => $filePath]);
                return false;
            }

            // 生成ZIP內的檔案名稱
            if (!$fileName) {
                $fileName = "applicant_{$applicantId}_merged.pdf";
            }

            // 確保檔案名稱唯一
            $fileName = $this->ensureUniqueFileName($zip, $fileName);

            // 添加檔案到ZIP
            $success = $zip->addFile($filePath, $fileName);

            if ($success) {
                Log::debug('檔案已添加到ZIP', [
                    'source_path' => $filePath,
                    'zip_name' => $fileName
                ]);
            } else {
                Log::warning('添加檔案到ZIP失敗', [
                    'source_path' => $filePath,
                    'zip_name' => $fileName
                ]);
            }

            return $success;
        } catch (\Exception $e) {
            Log::error('添加檔案到ZIP時發生錯誤', [
                'error' => $e->getMessage(),
                'file' => $file
            ]);
            return false;
        }
    }

    /**
     * 確保檔案名稱在ZIP中唯一
     *
     * @param ZipArchive $zip
     * @param string $fileName
     * @return string
     */
    protected function ensureUniqueFileName(ZipArchive $zip, string $fileName): string
    {
        $originalName = $fileName;
        $counter = 1;

        while ($zip->locateName($fileName) !== false) {
            $pathInfo = pathinfo($originalName);
            $fileName = $pathInfo['filename'] . "_{$counter}." . $pathInfo['extension'];
            $counter++;
        }

        return $fileName;
    }

    /**
     * 添加說明檔案到ZIP
     *
     * @param ZipArchive $zip
     * @param array $metadata
     * @param int $fileCount
     * @return void
     */
    protected function addReadmeToZip(ZipArchive $zip, array $metadata, int $fileCount): void
    {
        try {
            $readmeContent = $this->generateReadmeContent($metadata, $fileCount);
            $zip->addFromString('README.txt', $readmeContent);

            Log::debug('說明檔案已添加到ZIP');
        } catch (\Exception $e) {
            Log::warning('添加說明檔案到ZIP失敗', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 生成說明檔案內容
     *
     * @param array $metadata
     * @param int $fileCount
     * @return string
     */
    protected function generateReadmeContent(array $metadata, int $fileCount): string
    {
        $content = "推薦函合併檔案說明\n";
        $content .= "===================\n\n";
        $content .= "生成時間: " . date('Y-m-d H:i:s') . "\n";
        $content .= "檔案數量: {$fileCount}\n\n";

        if (isset($metadata['exam_id'])) {
            $content .= "招生代碼: " . $metadata['exam_id'] . "\n";
        }

        if (isset($metadata['exam_year'])) {
            $content .= "招生年度: " . $metadata['exam_year'] . "\n";
        }

        if (isset($metadata['department_name'])) {
            $content .= "系所名稱: " . $metadata['department_name'] . "\n";
        }

        $content .= "\n檔案說明:\n";
        $content .= "- 每個PDF檔案代表一位考生的所有推薦函合併結果\n";
        $content .= "- 檔案命名格式: [考生流水號].pdf\n";
        $content .= "- PDF內包含書籤，方便快速導航到不同推薦人的推薦函\n\n";

        $content .= "注意事項:\n";
        $content .= "- 此檔案由推薦函管理系統自動生成\n";
        $content .= "- 請妥善保管，避免外洩考生個人資料\n";
        $content .= "- 如有疑問，請聯繫系統管理員\n";

        return $content;
    }

    /**
     * 確保目錄存在
     *
     * @param string $directory
     * @return void
     */
    protected function ensureDirectoryExists(string $directory): void
    {
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }
    }

    /**
     * 清理臨時檔案
     *
     * @param array $files 要清理的檔案列表
     * @return int 清理的檔案數量
     */
    public function cleanupTemporaryFiles(array $files): int
    {
        $cleanedCount = 0;

        foreach ($files as $file) {
            $filePath = $file['file_path'] ?? null;

            if ($filePath && file_exists($filePath)) {
                try {
                    unlink($filePath);
                    $cleanedCount++;

                    Log::debug('臨時檔案已清理', ['file_path' => $filePath]);
                } catch (\Exception $e) {
                    Log::warning('清理臨時檔案失敗', [
                        'file_path' => $filePath,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        if ($cleanedCount > 0) {
            Log::info('臨時檔案清理完成', ['cleaned_count' => $cleanedCount]);
        }

        return $cleanedCount;
    }

    /**
     * 檢查ZIP檔案是否有效
     *
     * @param string $zipFilePath
     * @return bool
     */
    public function validateZipFile(string $zipFilePath): bool
    {
        try {
            if (!file_exists($zipFilePath)) {
                return false;
            }

            $zip = new ZipArchive();
            $result = $zip->open($zipFilePath, ZipArchive::CHECKCONS);

            if ($result === TRUE) {
                $zip->close();
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('驗證ZIP檔案失敗', [
                'zip_path' => $zipFilePath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
