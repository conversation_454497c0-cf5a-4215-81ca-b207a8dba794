<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

/**
 * 檔案打包服務
 * 
 * 負責將合併後的PDF檔案打包成ZIP檔案供下載
 */
class FilePackagingService
{
    /**
     * 創建直接壓縮的ZIP檔案（不合併PDF）
     *
     * @param array $files 檔案列表
     * @param string $taskId 任務ID
     * @param array $metadata 額外的元數據
     * @return string ZIP檔案路徑
     */
    public function createDirectZipPackage(array $files, string $taskId, array $metadata = []): string
    {
        try {
            if (empty($files)) {
                throw new \Exception('沒有提供要打包的檔案');
            }

            // 使用 public 磁碟以便外部訪問
            $disk = Storage::disk('public');

            // 生成ZIP檔案名稱和路徑
            $zipFileName = $this->generateZipFileName($taskId, $metadata);
            $zipFilePath = 'pdf_merges/' . $zipFileName;

            // 確保目錄存在
            if (!$disk->exists('pdf_merges')) {
                $disk->makeDirectory('pdf_merges', 0755, true);
            }

            $fullZipPath = $disk->path($zipFilePath);

            // 創建ZIP檔案
            $zip = new ZipArchive();
            $result = $zip->open($fullZipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

            if ($result !== TRUE) {
                throw new \Exception("無法創建ZIP檔案: {$result} (路徑: {$fullZipPath})");
            }

            // 添加檔案到ZIP
            $addedFiles = 0;
            foreach ($files as $file) {
                if ($this->addDirectFileToZip($zip, $file)) {
                    $addedFiles++;
                }
            }

            // 添加說明檔案
            $this->addDirectReadmeToZip($zip, $metadata, $addedFiles);

            $zip->close();

            if ($addedFiles === 0) {
                throw new \Exception('沒有成功添加任何檔案到ZIP');
            }

            // 設定檔案權限
            chmod($fullZipPath, 0644);

            Log::info('ZIP檔案創建成功', [
                'task_id' => $taskId,
                'zip_path' => $zipFilePath,
                'full_path' => $fullZipPath,
                'file_count' => $addedFiles,
                'file_size' => filesize($fullZipPath)
            ]);

            return $zipFilePath;
        } catch (\Exception $e) {
            Log::error('ZIP檔案創建失敗', [
                'task_id' => $taskId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 創建ZIP打包檔案
     *
     * @param array $files 檔案列表
     * @param string $taskId 任務ID
     * @param array $metadata 額外的元數據
     * @return string ZIP檔案路徑
     */
    public function createZipPackage(array $files, string $taskId, array $metadata = []): string
    {
        try {
            if (empty($files)) {
                throw new \Exception('沒有提供要打包的檔案');
            }

            // 生成ZIP檔案名稱和路徑
            $zipFileName = $this->generateZipFileName($taskId, $metadata);
            $zipFilePath = 'pdf_merges/' . $zipFileName;
            $fullZipPath = storage_path('app/' . $zipFilePath);

            // 確保目錄存在
            $this->ensureDirectoryExists(dirname($fullZipPath));

            // 創建ZIP檔案
            $zip = new ZipArchive();
            $result = $zip->open($fullZipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

            if ($result !== TRUE) {
                throw new \Exception("無法創建ZIP檔案: {$result}");
            }

            // 添加檔案到ZIP
            $addedFiles = 0;
            foreach ($files as $file) {
                if ($this->addFileToZip($zip, $file)) {
                    $addedFiles++;
                }
            }

            // 添加說明檔案
            $this->addReadmeToZip($zip, $metadata, $addedFiles);

            $zip->close();

            if ($addedFiles === 0) {
                throw new \Exception('沒有成功添加任何檔案到ZIP');
            }

            Log::info('ZIP檔案創建成功', [
                'zip_path' => $zipFilePath,
                'added_files' => $addedFiles,
                'total_files' => count($files),
                'task_id' => $taskId
            ]);

            return $zipFilePath;
        } catch (\Exception $e) {
            Log::error('創建ZIP檔案失敗', [
                'error' => $e->getMessage(),
                'task_id' => $taskId,
                'file_count' => count($files)
            ]);

            throw new \Exception('創建ZIP檔案失敗: ' . $e->getMessage());
        }
    }

    /**
     * 生成ZIP檔案名稱
     *
     * @param string $taskId
     * @param array $metadata
     * @return string
     */
    protected function generateZipFileName(string $taskId, array $metadata): string
    {
        $timestamp = date('Y-m-d_H-i-s');

        // 根據 exam_id 與 exam_year 產生檔案名稱
        $examId = $metadata['exam_id'] ?? 'unknown';
        $examYear = $metadata['exam_year'] ?? 'unknown';

        return "recommendations_{$examId}_{$examYear}_{$timestamp}.zip";
    }

    /**
     * 添加檔案到ZIP（直接壓縮模式）
     *
     * @param ZipArchive $zip
     * @param array $file
     * @return bool
     */
    protected function addDirectFileToZip(ZipArchive $zip, array $file): bool
    {
        try {
            $sourcePath = $file['source_path'] ?? null;
            $zipPath = $file['zip_path'] ?? null;
            $applicantId = $file['applicant_id'] ?? 'unknown';

            // 檢查源文件是否存在
            if (!$sourcePath) {
                Log::warning('未提供源文件路徑', ['file' => $file]);
                return false;
            }

            // 如果源路徑不是絕對路徑，嘗試從 storage 構建
            if (!file_exists($sourcePath)) {
                // 嘗試從 local 磁碟獲取
                $localDisk = Storage::disk('local');
                $relativePath = str_replace(storage_path('app/'), '', $sourcePath);

                if ($localDisk->exists($relativePath)) {
                    $sourcePath = $localDisk->path($relativePath);
                } else {
                    Log::warning('檔案不存在', [
                        'original_path' => $file['source_path'],
                        'tried_path' => $sourcePath,
                        'relative_path' => $relativePath,
                        'local_disk_exists' => $localDisk->exists($relativePath)
                    ]);
                    return false;
                }
            }

            if (!$zipPath) {
                $autono = $file['autono'] ?? $applicantId;
                $zipPath = "{$autono}.pdf";
            }

            // 確保ZIP內的目錄結構存在
            $zipDir = dirname($zipPath);
            if ($zipDir !== '.' && $zipDir !== '') {
                // 創建目錄結構
                $this->ensureZipDirectoryExists($zip, $zipDir);
            }

            // 添加檔案到ZIP
            $success = $zip->addFile($sourcePath, $zipPath);

            if ($success) {
                Log::debug('檔案已添加到ZIP', [
                    'source_path' => $sourcePath,
                    'zip_path' => $zipPath,
                    'file_size' => filesize($sourcePath)
                ]);
            } else {
                Log::warning('添加檔案到ZIP失敗', [
                    'source_path' => $sourcePath,
                    'zip_path' => $zipPath,
                    'file_exists' => file_exists($sourcePath),
                    'file_readable' => is_readable($sourcePath)
                ]);
            }

            return $success;
        } catch (\Exception $e) {
            Log::error('添加檔案到ZIP時發生錯誤', [
                'error' => $e->getMessage(),
                'file' => $file,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 添加檔案到ZIP
     *
     * @param ZipArchive $zip
     * @param array $file
     * @return bool
     */
    protected function addFileToZip(ZipArchive $zip, array $file): bool
    {
        try {
            $filePath = $file['file_path'] ?? null;
            $fileName = $file['file_name'] ?? null;
            $applicantId = $file['applicant_id'] ?? 'unknown';

            if (!$filePath || !file_exists($filePath)) {
                Log::warning('檔案不存在，跳過添加到ZIP', ['file_path' => $filePath]);
                return false;
            }

            // 生成ZIP內的檔案名稱
            if (!$fileName) {
                $fileName = "applicant_{$applicantId}_merged.pdf";
            }

            // 確保檔案名稱唯一
            $fileName = $this->ensureUniqueFileName($zip, $fileName);

            // 添加檔案到ZIP
            $success = $zip->addFile($filePath, $fileName);

            if ($success) {
                Log::debug('檔案已添加到ZIP', [
                    'source_path' => $filePath,
                    'zip_name' => $fileName
                ]);
            } else {
                Log::warning('添加檔案到ZIP失敗', [
                    'source_path' => $filePath,
                    'zip_name' => $fileName
                ]);
            }

            return $success;
        } catch (\Exception $e) {
            Log::error('添加檔案到ZIP時發生錯誤', [
                'error' => $e->getMessage(),
                'file' => $file
            ]);
            return false;
        }
    }

    /**
     * 確保檔案名稱在ZIP中唯一
     *
     * @param ZipArchive $zip
     * @param string $fileName
     * @return string
     */
    protected function ensureUniqueFileName(ZipArchive $zip, string $fileName): string
    {
        $originalName = $fileName;
        $counter = 1;

        while ($zip->locateName($fileName) !== false) {
            $pathInfo = pathinfo($originalName);
            $fileName = $pathInfo['filename'] . "_{$counter}." . $pathInfo['extension'];
            $counter++;
        }

        return $fileName;
    }

    /**
     * 確保ZIP內的目錄結構存在
     *
     * @param ZipArchive $zip
     * @param string $directory
     * @return void
     */
    protected function ensureZipDirectoryExists(ZipArchive $zip, string $directory): void
    {
        // 分解目錄路徑
        $parts = explode('/', $directory);
        $currentPath = '';

        foreach ($parts as $part) {
            if ($part === '') continue;

            $currentPath .= ($currentPath === '' ? '' : '/') . $part;

            // 檢查目錄是否已存在於ZIP中
            if ($zip->locateName($currentPath . '/') === false) {
                // 添加空目錄到ZIP
                $zip->addEmptyDir($currentPath);
            }
        }
    }

    /**
     * 添加說明檔案到ZIP（直接壓縮模式）
     *
     * @param ZipArchive $zip
     * @param array $metadata
     * @param int $fileCount
     * @return void
     */
    protected function addDirectReadmeToZip(ZipArchive $zip, array $metadata, int $fileCount): void
    {
        try {
            $readmeContent = $this->generateDirectReadmeContent($metadata, $fileCount);
            $zip->addFromString('README.txt', $readmeContent);

            Log::debug('說明檔案已添加到ZIP');
        } catch (\Exception $e) {
            Log::warning('添加說明檔案到ZIP失敗', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 添加說明檔案到ZIP
     *
     * @param ZipArchive $zip
     * @param array $metadata
     * @param int $fileCount
     * @return void
     */
    protected function addReadmeToZip(ZipArchive $zip, array $metadata, int $fileCount): void
    {
        try {
            $readmeContent = $this->generateReadmeContent($metadata, $fileCount);
            $zip->addFromString('README.txt', $readmeContent);

            Log::debug('說明檔案已添加到ZIP');
        } catch (\Exception $e) {
            Log::warning('添加說明檔案到ZIP失敗', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 生成說明檔案內容（直接壓縮模式）
     *
     * @param array $metadata
     * @param int $fileCount
     * @return string
     */
    protected function generateDirectReadmeContent(array $metadata, int $fileCount): string
    {
        $content = "推薦函檔案壓縮包說明\n";
        $content .= "===================\n\n";
        $content .= "生成時間: " . date('Y-m-d H:i:s') . "\n";
        $content .= "檔案數量: {$fileCount}\n\n";

        if (isset($metadata['exam_id'])) {
            $content .= "招生代碼: " . $metadata['exam_id'] . "\n";
        }

        if (isset($metadata['exam_year'])) {
            $content .= "招生年度: " . $metadata['exam_year'] . "\n";
        }

        if (isset($metadata['department_name'])) {
            $content .= "系所名稱: " . $metadata['department_name'] . "\n";
        }

        $content .= "\n檔案說明:\n";
        $content .= "- 每個PDF檔案代表一位考生的推薦函原始檔案\n";
        $content .= "- 檔案按照考生流水號(autono)分類到各自資料夾\n";
        $content .= "- 資料夾內推薦函以索引命名: 1.pdf, 2.pdf, 3.pdf...\n";
        $content .= "- 目錄結構: [autono]/[index].pdf\n\n";

        $content .= "目錄結構範例:\n";
        $content .= "348/\n";
        $content .= "├── 1.pdf  (第一封推薦函)\n";
        $content .= "├── 2.pdf  (第二封推薦函)\n";
        $content .= "349/\n";
        $content .= "├── 1.pdf\n";
        $content .= "└── 2.pdf\n\n";

        $content .= "注意事項:\n";
        $content .= "- 此檔案由推薦函管理系統自動生成\n";
        $content .= "- 檔案已按照外部系統要求的目錄結構組織\n";
        $content .= "- 請妥善保管，避免外洩考生個人資料\n";
        $content .= "- 如有疑問，請聯繫系統管理員\n";

        return $content;
    }

    /**
     * 生成說明檔案內容
     *
     * @param array $metadata
     * @param int $fileCount
     * @return string
     */
    protected function generateReadmeContent(array $metadata, int $fileCount): string
    {
        $content = "推薦函合併檔案說明\n";
        $content .= "===================\n\n";
        $content .= "生成時間: " . date('Y-m-d H:i:s') . "\n";
        $content .= "檔案數量: {$fileCount}\n\n";

        if (isset($metadata['exam_id'])) {
            $content .= "招生代碼: " . $metadata['exam_id'] . "\n";
        }

        if (isset($metadata['exam_year'])) {
            $content .= "招生年度: " . $metadata['exam_year'] . "\n";
        }

        if (isset($metadata['department_name'])) {
            $content .= "系所名稱: " . $metadata['department_name'] . "\n";
        }

        $content .= "\n檔案說明:\n";
        $content .= "- 每個PDF檔案代表一位考生的所有推薦函合併結果\n";
        $content .= "- 檔案命名格式: [考生流水號].pdf\n";
        $content .= "- PDF內包含書籤，方便快速導航到不同推薦人的推薦函\n\n";

        $content .= "注意事項:\n";
        $content .= "- 此檔案由推薦函管理系統自動生成\n";
        $content .= "- 請妥善保管，避免外洩考生個人資料\n";
        $content .= "- 如有疑問，請聯繫系統管理員\n";

        return $content;
    }

    /**
     * 確保目錄存在
     *
     * @param string $directory
     * @return void
     */
    protected function ensureDirectoryExists(string $directory): void
    {
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }
    }

    /**
     * 清理臨時檔案
     *
     * @param array $files 要清理的檔案列表
     * @return int 清理的檔案數量
     */
    public function cleanupTemporaryFiles(array $files): int
    {
        $cleanedCount = 0;

        foreach ($files as $file) {
            $filePath = $file['file_path'] ?? null;

            if ($filePath && file_exists($filePath)) {
                try {
                    unlink($filePath);
                    $cleanedCount++;

                    Log::debug('臨時檔案已清理', ['file_path' => $filePath]);
                } catch (\Exception $e) {
                    Log::warning('清理臨時檔案失敗', [
                        'file_path' => $filePath,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        if ($cleanedCount > 0) {
            Log::info('臨時檔案清理完成', ['cleaned_count' => $cleanedCount]);
        }

        return $cleanedCount;
    }

    /**
     * 檢查ZIP檔案是否有效
     *
     * @param string $zipFilePath
     * @return bool
     */
    public function validateZipFile(string $zipFilePath): bool
    {
        try {
            if (!file_exists($zipFilePath)) {
                return false;
            }

            $zip = new ZipArchive();
            $result = $zip->open($zipFilePath, ZipArchive::CHECKCONS);

            if ($result === TRUE) {
                $zip->close();
                return true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('驗證ZIP檔案失敗', [
                'zip_path' => $zipFilePath,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
