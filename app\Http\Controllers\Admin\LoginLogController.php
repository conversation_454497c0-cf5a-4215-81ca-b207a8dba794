<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LoginLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use App\Models\SystemLog;

/**
 * 登入記錄控制器
 * 
 * 管理系統的使用者登入記錄
 */
class LoginLogController extends Controller
{
    /**
     * 顯示登入記錄列表
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        try {
            $loginLogs = LoginLog::with('user:id,name,email,role')
                ->orderBy('login_at', 'desc')
                ->paginate(20);

            return Inertia::render('admin/login-logs', [
                'loginLogs' => $loginLogs,
            ]);
        } catch (\Exception $e) {
            Log::error('Login logs index error: ' . $e->getMessage());

            return back()->withErrors([
                'system' => '載入登入記錄失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 顯示特定登入記錄詳情
     *
     * @param int $id
     * @return \Inertia\Response
     */
    public function show(int $id)
    {
        try {
            $loginLog = LoginLog::with('user')
                ->findOrFail($id);

            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                '查看登入記錄詳情',
                ['login_log_id' => $id]
            );

            return Inertia::render('admin/login-log-detail', [
                'loginLog' => $loginLog,
            ]);
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_VIEW,
                '查看登入記錄詳情失敗',
                $e
            );

            return back()->withErrors([
                'system' => '載入登入記錄詳情失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 清理舊的登入記錄
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cleanup(Request $request)
    {
        try {
            $days = $request->input('days', 90);

            $deletedCount = LoginLog::where('login_at', '<', now()->subDays($days))
                ->delete();

            SystemLog::logOperation(
                SystemLog::ACTION_DELETE,
                '清理舊的登入記錄',
                [
                    'days' => $days,
                    'deleted_count' => $deletedCount
                ]
            );

            return response()->json([
                'success' => true,
                'message' => "已清理 {$deletedCount} 筆舊的登入記錄",
                'deleted_count' => $deletedCount
            ]);
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_DELETE,
                '清理登入記錄失敗',
                $e
            );

            return response()->json([
                'success' => false,
                'message' => '清理失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 匯出登入記錄
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        try {
            $startDate = $request->input('start_date', now()->subDays(30)->format('Y-m-d'));
            $endDate = $request->input('end_date', now()->format('Y-m-d'));

            $loginLogs = LoginLog::with('user:id,name,email,role')
                ->whereBetween('login_at', [$startDate, $endDate])
                ->orderBy('login_at', 'desc')
                ->get();

            SystemLog::logOperation(
                SystemLog::ACTION_EXPORT,
                '匯出登入記錄',
                [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'record_count' => $loginLogs->count()
                ]
            );

            $filename = "login_logs_{$startDate}_to_{$endDate}.csv";

            return response()->streamDownload(function () use ($loginLogs) {
                $handle = fopen('php://output', 'w');

                // CSV 標題
                fputcsv($handle, [
                    '登入時間',
                    '使用者姓名',
                    '使用者信箱',
                    '使用者角色',
                    'IP 位址',
                    '使用者代理',
                    '登入方式'
                ]);

                // 資料行
                foreach ($loginLogs as $log) {
                    fputcsv($handle, [
                        $log->login_at,
                        $log->user->name ?? 'N/A',
                        $log->user->email ?? 'N/A',
                        $log->user->role ?? 'N/A',
                        $log->ip_address,
                        $log->user_agent,
                        $log->login_method
                    ]);
                }

                fclose($handle);
            }, $filename, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ]);
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_EXPORT,
                '匯出登入記錄失敗',
                $e
            );

            return back()->withErrors([
                'system' => '匯出失敗，請稍後再試'
            ]);
        }
    }
}
