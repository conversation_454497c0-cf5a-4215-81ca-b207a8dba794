import { Button } from '@/components/ui/button';
import { type Toast, type ToastType } from '@/hooks/useToast';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, Info, X, AlertTriangle } from 'lucide-react';
import { useEffect, useState } from 'react';

interface ToastProps {
    toast: Toast;
    onRemove: (id: string) => void;
}

const toastIcons: Record<ToastType, React.ComponentType<{ className?: string }>> = {
    success: CheckCircle,
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info,
};

const toastStyles: Record<ToastType, string> = {
    success: 'border-green-200 bg-green-50 text-green-800',
    error: 'border-red-200 bg-red-50 text-red-800',
    warning: 'border-yellow-200 bg-yellow-50 text-yellow-800',
    info: 'border-blue-200 bg-blue-50 text-blue-800',
};

export function ToastItem({ toast, onRemove }: ToastProps) {
    const [isVisible, setIsVisible] = useState(false);
    const [isLeaving, setIsLeaving] = useState(false);

    const Icon = toastIcons[toast.type];

    useEffect(() => {
        // Trigger entrance animation
        const timer = setTimeout(() => setIsVisible(true), 10);
        return () => clearTimeout(timer);
    }, []);

    const handleRemove = () => {
        setIsLeaving(true);
        setTimeout(() => onRemove(toast.id), 300); // Match animation duration
    };

    return (
        <div
            className={cn(
                'pointer-events-auto flex w-full max-w-sm transform rounded-lg border p-4 shadow-lg transition-all duration-300 ease-in-out',
                toastStyles[toast.type],
                isVisible && !isLeaving
                    ? 'translate-x-0 opacity-100'
                    : 'translate-x-full opacity-0'
            )}
        >
            <div className="flex items-start space-x-3 flex-1">
                <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
                <div className="flex-1 space-y-1">
                    {toast.title && (
                        <h4 className="text-sm font-medium">{toast.title}</h4>
                    )}
                    <p className="text-sm">{toast.message}</p>
                    {toast.action && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={toast.action.onClick}
                            className="h-auto p-0 text-current hover:text-current/80"
                        >
                            {toast.action.label}
                        </Button>
                    )}
                </div>
            </div>
            <Button
                variant="ghost"
                size="sm"
                onClick={handleRemove}
                className="h-auto p-1 text-current hover:text-current/80 flex-shrink-0"
            >
                <X className="h-4 w-4" />
            </Button>
        </div>
    );
}

interface ToastContainerProps {
    toasts: Toast[];
    onRemove: (id: string) => void;
    position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

const positionStyles = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
};

export function ToastContainer({ toasts, onRemove, position = 'top-right' }: ToastContainerProps) {
    if (toasts.length === 0) {
        return null;
    }

    return (
        <div
            className={cn(
                'pointer-events-none fixed z-50 flex flex-col space-y-2',
                positionStyles[position]
            )}
        >
            {toasts.map((toast) => (
                <ToastItem key={toast.id} toast={toast} onRemove={onRemove} />
            ))}
        </div>
    );
}
