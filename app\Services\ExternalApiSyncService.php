<?php

namespace App\Services;

use App\Models\SystemSetting;
use App\Models\SystemLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

/**
 * 外部API同步服務
 * 
 * 負責與外部系統進行資料同步，包括招生期間、考試資訊等
 */
class ExternalApiSyncService
{
    /**
     * 獲取API基礎URL
     */
    private function getApiBaseUrl(): string
    {
        return config('api.base_url');
    }

    /**
     * 獲取API密鑰
     */
    private function getApiSecret(): string
    {
        return config('api.secret');
    }

    /**
     * 獲取API超時設定
     */
    private function getApiTimeout(): int
    {
        return (int)config('api.timeout');
    }

    /**
     * 同步招生期間資料
     * 
     * @return array 同步結果
     */
    public function syncExamPeriods(): array
    {
        try {
            $apiUrl = $this->getApiBaseUrl() . '/sync_exam_period';

            Log::info('開始同步招生期間資料', ['api_url' => $apiUrl]);

            $response = Http::timeout($this->getApiTimeout())
                ->asForm()
                ->post($apiUrl, [
                    'nuu_api_key' => $this->getApiSecret(),
                    'nuu_api_id' => 'sync_exam_period',
                ]);

            if (!$response->successful()) {
                throw new \Exception('API 請求失敗，狀態碼：' . $response->status());
            }

            $data = $response->json();

            if (!isset($data['status']) || $data['status'] !== 'success') {
                throw new \Exception('API 回傳非 success：' . json_encode($data));
            }

            // 處理同步的資料
            $examPeriods = $data['data'] ?? [];
            $syncResult = $this->processExamPeriods($examPeriods);

            SystemLog::logOperation(
                SystemLog::ACTION_SYSTEM_SETTING,
                '成功同步招生期間資料',
                [
                    'synced_periods' => count($examPeriods),
                    'updated_settings' => $syncResult['updated_count']
                ]
            );

            return [
                'success' => true,
                'message' => '招生期間資料同步成功',
                'data' => $syncResult
            ];
        } catch (\Exception $e) {
            Log::error('同步招生期間資料失敗', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            SystemLog::logError(
                SystemLog::ACTION_SYSTEM_SETTING,
                '同步招生期間資料失敗',
                $e
            );

            // 設定預設值
            $defaultResult = $this->setDefaultExamPeriods();

            return [
                'success' => false,
                'message' => '同步失敗，已設定預設值：' . $e->getMessage(),
                'data' => $defaultResult
            ];
        }
    }

    /**
     * 處理招生期間資料
     *
     * @param array $examPeriods
     * @return array
     */
    private function processExamPeriods(array $examPeriods): array
    {
        $processedPeriods = [];

        foreach ($examPeriods as $period) {
            if (!isset($period['exam_id'], $period['exam_name'], $period['app_date1_start'], $period['app_date1_end'])) {
                Log::warning('招生期間資料格式不完整', ['period' => $period]);
                continue;
            }

            $processedPeriods[] = [
                'exam_id' => $period['exam_id'],
                'exam_name' => $period['exam_name'],
                'app_date1_start' => $period['app_date1_start'],
                'app_date1_end' => $period['app_date1_end'],
                'synced_at' => now()->toISOString()
            ];
        }

        // 將所有招生期間資料以JSON格式統一保存
        SystemSetting::set(
            'recruitment_periods_data',
            json_encode($processedPeriods, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT),
            SystemSetting::TYPE_JSON,
            SystemSetting::CATEGORY_TIMING,
            '招生期間資料（從外部API同步）'
        );

        // 記錄同步時間
        SystemSetting::set(
            'recruitment_periods_last_sync',
            now()->toISOString(),
            SystemSetting::TYPE_DATETIME,
            SystemSetting::CATEGORY_TIMING,
            '招生期間資料最後同步時間'
        );

        // 清除相關快取
        Cache::forget('system_settings_recruitment');
        Cache::forget('recruitment_periods_data');

        Log::info('招生期間資料已保存', [
            'periods_count' => count($processedPeriods),
            'data' => $processedPeriods
        ]);

        return [
            'updated_count' => count($processedPeriods),
            'processed_periods' => $processedPeriods
        ];
    }

    /**
     * 設定預設招生期間
     * 
     * @return array
     */
    private function setDefaultExamPeriods(): array
    {
        $defaultPeriods = [
            [
                'exam_id' => 'default',
                'exam_name' => '預設招生期間',
                'app_date1_start' => now()->format('Y-m-d H:i:s'),
                'app_date1_end' => now()->addMonths(3)->format('Y-m-d H:i:s')
            ]
        ];

        Log::info('設定預設招生期間', ['periods' => $defaultPeriods]);

        return $this->processExamPeriods($defaultPeriods);
    }

    /**
     * 同步系統基本設定
     * 
     * @return array
     */
    public function syncSystemSettings(): array
    {
        try {
            // 這裡可以擴展其他系統設定的同步
            $results = [];

            // 同步招生期間
            $examResult = $this->syncExamPeriods();
            $results['exam_periods'] = $examResult;

            $overallSuccess = $examResult['success'];

            return [
                'success' => $overallSuccess,
                'message' => $overallSuccess ? '系統設定同步完成' : '部分設定同步失敗',
                'results' => $results
            ];
        } catch (\Exception $e) {
            Log::error('同步系統設定失敗', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '系統設定同步失敗：' . $e->getMessage(),
                'results' => []
            ];
        }
    }

    /**
     * 檢查API連線狀態
     * 
     * @return array
     */
    public function checkApiConnection(): array
    {
        try {
            $response = Http::timeout(10)
                ->get($this->getApiBaseUrl() . '/health');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'API連線正常',
                    'response_time' => $response->transferStats?->getTransferTime() ?? 0
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'API連線異常，狀態碼：' . $response->status()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'API連線失敗：' . $e->getMessage()
            ];
        }
    }
}
