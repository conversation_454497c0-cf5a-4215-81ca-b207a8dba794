<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\RecommendationLetter;
use App\Models\Applicant;
use App\Models\SystemLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 推薦函API控制器
 * 
 * 提供外部系統查詢推薦函資料的API端點
 */
class RecommendationApiController extends Controller
{
    /**
     * 根據考生資訊查詢推薦函狀態
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecommendationsByApplicant(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'exam_id' => 'required|string',
                'stu_year' => 'required|string',
                'stu_idno' => 'required|string', // 加密的學號
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 查找考生
            $applicant = Applicant::where('exam_id', $request->exam_id)
                ->where('stu_year', $request->stu_year)
                ->where('external_uid', $request->stu_idno)
                ->first();

            if (!$applicant) {
                return response()->json([
                    'success' => false,
                    'message' => '找不到對應的考生資料'
                ], 404);
            }

            // 查詢推薦函
            $recommendations = RecommendationLetter::where('applicant_id', $applicant->id)
                ->with(['recommender'])
                ->get()
                ->map(function ($recommendation) {
                    return [
                        'id' => $recommendation->id,
                        'external_autono' => $recommendation->external_autono,
                        'department_name' => $recommendation->department_name,
                        'program_type' => $recommendation->program_type,
                        'status' => $recommendation->status,
                        'recommender_name' => $recommendation->recommender_name,
                        'recommender_email' => $recommendation->recommender_email,
                        'submitted_at' => $recommendation->submitted_at?->format('Y-m-d H:i:s'),
                        'created_at' => $recommendation->created_at->format('Y-m-d H:i:s'),
                        'updated_at' => $recommendation->updated_at->format('Y-m-d H:i:s'),
                    ];
                });

            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                'API查詢推薦函資料',
                [
                    'exam_id' => $request->exam_id,
                    'stu_year' => $request->stu_year,
                    'applicant_id' => $applicant->id,
                    'recommendations_count' => $recommendations->count()
                ]
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'applicant' => [
                        'id' => $applicant->id,
                        'exam_id' => $applicant->exam_id,
                        'stu_year' => $applicant->stu_year,
                        'name' => $applicant->user->name ?? null,
                        'email' => $applicant->user->email ?? null,
                    ],
                    'recommendations' => $recommendations
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('API查詢推薦函失敗', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            SystemLog::logError(
                SystemLog::ACTION_VIEW,
                'API查詢推薦函失敗',
                $e
            );

            return response()->json([
                'success' => false,
                'message' => '查詢失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 查詢推薦函統計資料
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecommendationStats(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'exam_id' => 'nullable|string',
                'stu_year' => 'nullable|string',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = RecommendationLetter::query();

            // 篩選條件
            if ($request->exam_id) {
                $query->whereHas('applicant', function ($q) use ($request) {
                    $q->where('exam_id', $request->exam_id);
                });
            }

            if ($request->stu_year) {
                $query->whereHas('applicant', function ($q) use ($request) {
                    $q->where('stu_year', $request->stu_year);
                });
            }

            if ($request->date_from) {
                $query->where('created_at', '>=', $request->date_from);
            }

            if ($request->date_to) {
                $query->where('created_at', '<=', $request->date_to . ' 23:59:59');
            }

            // 統計資料
            $stats = [
                'total' => $query->count(),
                'by_status' => $query->groupBy('status')
                    ->selectRaw('status, count(*) as count')
                    ->pluck('count', 'status')
                    ->toArray(),
                'submitted' => $query->whereNotNull('submitted_at')->count(),
                'pending' => $query->where('status', 'pending')->count(),
                'completed' => $query->where('status', 'completed')->count(),
                'declined' => $query->where('status', 'declined')->count(),
            ];

            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                'API查詢推薦函統計',
                [
                    'filters' => $request->only(['exam_id', 'stu_year', 'date_from', 'date_to']),
                    'total_count' => $stats['total']
                ]
            );

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('API查詢推薦函統計失敗', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '查詢統計失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 健康檢查端點
     * 
     * @return JsonResponse
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => '推薦函系統API運行正常',
            'timestamp' => now()->toISOString(),
            'version' => '1.0.0'
        ]);
    }

    /**
     * API資訊端點
     * 
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'system_name' => '推薦函管理系統',
                'api_version' => '1.0.0',
                'endpoints' => [
                    'GET /api/recommendations/by-applicant' => '根據考生資訊查詢推薦函',
                    'GET /api/recommendations/stats' => '查詢推薦函統計資料',
                    'GET /api/health' => '健康檢查',
                    'GET /api/info' => 'API資訊'
                ],
                'timestamp' => now()->toISOString()
            ]
        ]);
    }
}
