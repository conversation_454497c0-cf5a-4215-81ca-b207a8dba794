{"common": {"appName": "Recommendation System", "schoolName": "National United University", "schoolName_short": "NUU", "dashboard": "Dashboard", "settings": "Settings", "logout": "Logout", "profile": "Profile", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "submit": "Submit", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "confirm": "Confirm", "yes": "Yes", "no": "No", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "view": "View", "preview": "Preview", "print": "Print", "refresh": "Refresh", "clear": "Clear", "reset": "Reset", "unknown": "Unknown", "items": "items", "apply": "Apply", "remove": "Remove", "add": "Add", "select": "Select", "all": "All", "none": "None", "other": "Other", "platform": "Platform", "no_data": "No data available", "group": "Group"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "recommendations": "Recommendations", "templates": "Templates", "users": "User Management", "reports": "Reports", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact Us", "logout": "Logout", "profile": "Profile"}, "auth": {"dashboard": "Home", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "verifyEmail": "<PERSON><PERSON><PERSON>", "name": "Name", "phone": "Phone", "title": "Title", "department": "Department", "organization": "Organization"}, "dashboard": {"welcome": "Welcome", "overview": "Overview", "statistics": "Statistics", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "notifications": "Notifications", "applicant": {"importantNotice": "Important Notice", "deadline": "Recommendation Letter Deadline", "deadlineDate": "March 15, 2024 23:59", "deadlineNote": "Please ensure recommenders complete uploads before this date", "advanceTime": "Recommended Lead Time", "advanceTimeNote": "Please invite recommenders at least 7 days before the deadline to allow sufficient time", "requiredCount": "Required Recommendations", "requiredCountNote": "Each application group requires 2 recommendation letters, please ensure all groups meet the requirement", "reminderFunction": "Reminder Function", "reminderNote": "You can use the 'Send Reminder' function, but only once every 24 hours, please use wisely", "contact": "Contact Information", "contactNote": "For questions, please contact the Admissions Office", "systemTime": "System Time", "examGroup": "Application Group", "needComplete": "Need to complete", "completed": "Completed", "addRecommender": "Add Recommender", "recommenderInfo": "Recommender Information", "completedTime": "Completed Time", "createdTime": "Created Time", "sendReminder": "Send Reminder", "withdrawRecommendation": "Withdraw Recommendation", "noRegistrationData": "No registration data available, please return to the registration system to register", "overallProgress": "Overall Progress Overview"}, "recommender": {"importantNotice": "Important Notice", "deadline": "Recommendation Letter Deadline", "deadlineDate": "March 15, 2024 23:59", "deadlineNote": "Please complete all recommendation letters before this date", "profileMaintenance": "Profile Maintenance", "updateProfile": "Update Profile", "filterRecommendations": "Filter Recommendations", "sortByDate": "Sort by Date", "showPendingOnly": "Show Pending Only", "showAllStatus": "Show All Status", "pendingCount": "Pending Items", "completedCount": "Completed Items", "summary": "Summary", "applicantInfo": "Applicant Information", "invitationTime": "Invitation Time", "submissionTime": "Submission Time", "acceptInvitation": "Accept Invitation", "declineInvitation": "Decline Invitation", "fillRecommendation": "Fill Recommendation", "viewRecommendation": "View Recommendation", "submissionMethod": "Submission Method", "uploadPDF": "Upload PDF File", "fillQuestionnaire": "Fill Online Questionnaire", "previewTemplate": "Preview Template", "downloadTemplate": "Download Template", "additionalNotes": "Additional Notes"}}, "recommendations": {"title": "Recommendations", "create": "Create Recommendation", "edit": "Edit Recommendation", "view": "View Recommendation", "delete": "Delete Recommendation", "status": {"label": "Status", "pending": "Waiting for Recommender", "submitted": "Recommendation Uploaded", "withdrawn": "Withdrawn by Applicant", "no_response": "No Response (7+ days)", "declined": "Declined by Recommender", "completed": "Completed"}, "actions": {"send_reminder": "Send Reminder", "withdraw": "Withdraw Recommendation", "add_recommender": "Add Recommender", "create_new": "Create Recommendation"}, "form": {"recommenderName": "Recommender Name", "recommenderEmail": "Recommender Email", "recommenderTitle": "Recommender Title", "recommenderPhone": "Recommender Phone", "recommenderAffiliation": "Recommender Affiliation", "recommenderTitlePlaceholder": "e.g., Professor, Associate Professor, Assistant Professor, Lecturer, etc.", "recommenderAffiliationPlaceholder": "e.g., Department of Computer Science, National Taiwan University, Technical Department of Company, etc.", "department": "Department", "programType": "Program Type", "additionalNotes": "Additional Notes", "optional": "Optional, max 500 characters", "additionalNotesPlaceholder": "Please enter additional notes for the recommender, such as: application motivation, research direction, special requirements, etc...", "additionalNotesNote": "※ This note will be shown to the recommender for reference and cannot be modified after submission", "groupNote": "This recommendation letter will be associated with the above group", "submit": "Send Invitation", "cancel": "Cancel", "required": "Required", "submitting": "Submitting...", "validation_error": "Form Validation Error", "applicationFormId": "Application Form ID"}, "messages": {"createSuccess": "Recommendation created successfully", "updateSuccess": "Recommendation updated successfully", "deleteSuccess": "Recommendation deleted successfully", "submitSuccess": "Recommendation submitted successfully", "invitation_sent": "Invitation sent", "reminder_sent": "<PERSON><PERSON><PERSON> sent", "withdrawn": "Recommendation withdrawn", "confirm_withdraw": "Are you sure you want to withdraw this recommendation?", "confirm_send_reminder": "Are you sure you want to send a reminder to the recommender?", "remind_confirmation_message": "Are you sure you want to send a reminder to", "withdraw_confirmation_message": "Are you sure you want to withdraw the recommendation for", "reminderSent": "Reminder sent to recommender", "reminderFailed": "Failed to send reminder, please try again later", "withdrawFailed": "Failed to withdraw recommendation, please try again later", "no_recommendations_for_group": "No recommendations created for this group", "cooldown_active": "You can resend reminder after {hours} hours {minutes} minutes"}, "tabs": {"status": {"submitted": "🟢 Submitted", "pending": "🟡 Pending", "not_created": "🔴 Not Created"}}, "recommender": "Recommender", "applicant": "Applicant", "department": "Department", "program": "Program", "dueDate": "Due Date", "submittedAt": "Submitted At", "createdAt": "Created At", "updatedAt": "Updated At"}, "questionnaire": {"title": "Questionnaire", "template": "Template", "question": "Question", "answer": "Answer", "required": "Required", "optional": "Optional", "type": "Type", "text": "Text", "textarea": "Textarea", "select": "Select", "radio": "Radio", "checkbox": "Checkbox", "file": "File", "date": "Date", "number": "Number", "email": "Email", "url": "URL", "phone": "Phone"}, "settings": {"title": "Settings", "profile": "Profile", "account": "Account <PERSON><PERSON>", "security": "Security Settings", "notifications": "Notification Settings", "language": "Language Settings", "theme": "Theme Settings", "privacy": "Privacy Settings", "preferences": "Preferences", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "updateProfile": "Update Profile", "deleteAccount": "Delete Account", "exportData": "Export Data", "importData": "Import Data"}, "errors": {"general": "An error occurred", "network": "Network error", "server": "Server error", "notFound": "Page not found", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "validation": "Validation error", "required": "This field is required", "invalid": "Invalid format", "tooShort": "Too short", "tooLong": "Too long", "emailInvalid": "Invalid email format", "passwordTooWeak": "Password too weak", "passwordMismatch": "Passwords do not match", "fileTooBig": "File too large", "fileTypeNotSupported": "File type not supported"}, "success": {"saved": "Saved", "updated": "Updated", "created": "Created", "deleted": "Deleted", "submitted": "Submitted", "sent": "<PERSON><PERSON>", "uploaded": "Uploaded", "downloaded": "Downloaded", "exported": "Exported", "imported": "Imported"}, "roles": {"admin": "Administrator", "applicant": "Applicant", "recommender": "Recommender", "user": "User"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "draft": "Draft", "published": "Published", "archived": "Archived"}, "welcome": {"welcome": "Welcome to the Recommendation System", "welcomeDescription": "Welcome to the Recommendation System!", "applicantLogin": "Applicant Login", "manageApplications": "Manage Applications", "recommenderLogin": "Recommender Login", "fillRecommendation": "Fill Recommendation", "getStarted": "Get Started"}}