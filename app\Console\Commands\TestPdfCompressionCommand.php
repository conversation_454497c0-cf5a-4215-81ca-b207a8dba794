<?php

namespace App\Console\Commands;

use App\Jobs\ProcessPdfMergeJob;
use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 測試 PDF 壓縮功能的 Artisan 指令
 * 
 * 使用方式：
 * php artisan pdf:test-compression --exam_id=2 --exam_year=114
 * php artisan pdf:test-compression --exam_id=2 --exam_year=114 --dry-run
 * php artisan pdf:test-compression --list-available
 */
class TestPdfCompressionCommand extends Command
{
    /**
     * The name and signature of the console command.
     * 
     * @var string
     */
    protected $signature = 'pdf:test-compression 
                            {--exam_id= : 招生代碼}
                            {--exam_year= : 招生年度}
                            {--dry-run : 僅顯示會處理的檔案，不實際執行}
                            {--list-available : 列出可用的招生代碼和年度}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '測試 PDF 壓縮功能';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== PDF 壓縮功能測試 ===');

        // 如果要求列出可用選項
        if ($this->option('list-available')) {
            return $this->listAvailableOptions();
        }

        $examId = $this->option('exam_id');
        $examYear = $this->option('exam_year');
        $isDryRun = $this->option('dry-run');

        // 驗證參數
        if (!$examId || !$examYear) {
            $this->error('請提供 exam_id 和 exam_year 參數');
            $this->info('使用 --list-available 查看可用選項');
            return Command::FAILURE;
        }

        try {
            // 檢查可用的推薦函
            $recommendations = $this->getAvailableRecommendations($examId, $examYear);

            if ($recommendations->isEmpty()) {
                $this->error("沒有找到符合條件的推薦函 (exam_id: {$examId}, exam_year: {$examYear})");
                return Command::FAILURE;
            }

            $this->info("找到 {$recommendations->count()} 個推薦函");

            // 顯示詳細資訊
            $this->displayRecommendationDetails($recommendations);

            if ($isDryRun) {
                $this->info('');
                $this->info('=== 乾跑模式：不會實際執行壓縮 ===');
                return Command::SUCCESS;
            }

            // 確認執行
            if (!$this->confirm('是否要開始執行 PDF 壓縮測試？')) {
                $this->info('已取消執行');
                return Command::SUCCESS;
            }

            // 執行壓縮測試
            return $this->executeCompressionTest($examId, $examYear);

        } catch (\Exception $e) {
            $this->error('測試失敗: ' . $e->getMessage());
            Log::error('PDF壓縮測試失敗', [
                'exam_id' => $examId,
                'exam_year' => $examYear,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * 列出可用的招生代碼和年度
     */
    protected function listAvailableOptions(): int
    {
        $this->info('可用的招生代碼和年度：');
        $this->info('');

        $availableOptions = RecommendationLetter::select('exam_id', 'exam_year')
            ->where('status', RecommendationLetter::STATUS_SUBMITTED)
            ->whereNotNull('pdf_path')
            ->groupBy('exam_id', 'exam_year')
            ->orderBy('exam_year', 'desc')
            ->orderBy('exam_id')
            ->get();

        if ($availableOptions->isEmpty()) {
            $this->warn('沒有找到任何可用的推薦函資料');
            return Command::SUCCESS;
        }

        $headers = ['招生代碼', '招生年度', '推薦函數量'];
        $rows = [];

        foreach ($availableOptions as $option) {
            $count = RecommendationLetter::where('exam_id', $option->exam_id)
                ->where('exam_year', $option->exam_year)
                ->where('status', RecommendationLetter::STATUS_SUBMITTED)
                ->whereNotNull('pdf_path')
                ->count();

            $rows[] = [
                $option->exam_id,
                $option->exam_year,
                $count
            ];
        }

        $this->table($headers, $rows);

        $this->info('');
        $this->info('使用範例：');
        $this->info('php artisan pdf:test-compression --exam_id=2 --exam_year=114');
        $this->info('php artisan pdf:test-compression --exam_id=2 --exam_year=114 --dry-run');

        return Command::SUCCESS;
    }

    /**
     * 獲取可用的推薦函
     */
    protected function getAvailableRecommendations(string $examId, string $examYear)
    {
        return RecommendationLetter::where('exam_id', $examId)
            ->where('exam_year', $examYear)
            ->where('status', RecommendationLetter::STATUS_SUBMITTED)
            ->whereNotNull('pdf_path')
            ->with(['applicant', 'recommender'])
            ->get();
    }

    /**
     * 顯示推薦函詳細資訊
     */
    protected function displayRecommendationDetails($recommendations): void
    {
        $this->info('');
        $this->info('推薦函詳細資訊：');

        // 按考生分組
        $groupedByApplicant = $recommendations->groupBy('applicant_id');

        $headers = ['考生ID', '考生流水號', '推薦函數量', 'PDF檔案狀態'];
        $rows = [];

        foreach ($groupedByApplicant as $applicantId => $applicantRecommendations) {
            $firstRec = $applicantRecommendations->first();
            $autono = $firstRec->external_autono ?? $applicantId;
            
            $pdfStatus = [];
            foreach ($applicantRecommendations as $rec) {
                $pdfPath = storage_path('app/' . $rec->pdf_path);
                $status = file_exists($pdfPath) ? '✓' : '✗';
                $size = file_exists($pdfPath) ? $this->formatFileSize(filesize($pdfPath)) : 'N/A';
                $pdfStatus[] = "{$status} ({$size})";
            }

            $rows[] = [
                $applicantId,
                $autono,
                $applicantRecommendations->count(),
                implode(', ', $pdfStatus)
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * 執行壓縮測試
     */
    protected function executeCompressionTest(string $examId, string $examYear): int
    {
        $this->info('');
        $this->info('=== 開始執行 PDF 壓縮測試 ===');

        // 準備參數
        $parameters = [
            'exam_id' => $examId,
            'exam_year' => $examYear,
            'requested_at' => now()->toISOString(),
            'client_ip' => '127.0.0.1',
            'created_by' => 'test_command',
            'test_mode' => true
        ];

        // 創建測試任務
        $task = PdfMergeTask::createTask($parameters);
        
        $this->info("已創建測試任務: {$task->task_id}");

        // 執行任務
        $this->info('正在執行壓縮...');
        
        try {
            $job = new ProcessPdfMergeJob($task->task_id, $parameters);
            $job->handle();

            // 檢查結果
            $task->refresh();

            if ($task->status === PdfMergeTask::STATUS_READY) {
                $this->info('✓ 壓縮測試成功完成！');
                $this->info("ZIP檔案路徑: {$task->zip_file_path}");
                $this->info("下載URL: {$task->download_url}");
                
                // 顯示檔案資訊
                if ($task->zip_file_path) {
                    $zipPath = storage_path('app/' . $task->zip_file_path);
                    if (file_exists($zipPath)) {
                        $size = $this->formatFileSize(filesize($zipPath));
                        $this->info("ZIP檔案大小: {$size}");
                    }
                }

                return Command::SUCCESS;
            } else {
                $this->error('✗ 壓縮測試失敗');
                $this->error("錯誤訊息: {$task->error_message}");
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error('✗ 執行過程中發生錯誤: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * 格式化檔案大小
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
