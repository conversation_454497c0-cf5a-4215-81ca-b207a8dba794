<?php

namespace App\Http\Controllers\Admin;

use App\Models\RecommendationLetter;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class AdminRecommendationController extends Controller
{
    /**
     * 取得所有推薦函資料供管理員使用
     */
    public function index(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user || $user->role !== 'admin') {
                abort(403, '您沒有權限查看此資料');
            }

            $query = RecommendationLetter::with(['applicant.user']);

            // Apply filters if provided
            if ($request->has('status') && $request->status !== 'all') {
                $query->where('status', $request->status);
            }

            if ($request->has('department') && $request->department !== 'all') {
                $query->where('department_name', $request->department);
            }

            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('recommender_name', 'like', "%{$search}%")
                        ->orWhere('recommender_email', 'like', "%{$search}%")
                        ->orWhere('department_name', 'like', "%{$search}%")
                        ->orWhereHas('applicant.user', function ($userQuery) use ($search) {
                            $userQuery->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%");
                        });
                });
            }

            $recommendations = $query->orderBy('created_at', 'desc')->get();

            return Inertia::render('admin/recommendations', [
                'recommendations' => $recommendations,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching admin recommendations: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            return back()->withErrors([
                'system' => '無法載入推薦函清單，請重新整理頁面'
            ]);
        }
    }

    /**
     * Get recommendation letter details for admin.
     */
    public function show(Request $request, $id)
    {
        try {
            $user = Auth::user();

            if (!$user || $user->role !== 'admin') {
                abort(403, '您沒有權限查看此資料');
            }

            $recommendation = RecommendationLetter::with(['applicant.user'])
                ->find($id);

            if (!$recommendation) {
                abort(404, '找不到指定的推薦函');
            }

            return Inertia::render('admin/recommendation-detail', [
                'recommendation' => $recommendation,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching recommendation details: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'recommendation_id' => $id
            ]);

            return back()->withErrors([
                'system' => '無法載入推薦函詳情，請稍後再試'
            ]);
        }
    }
}
