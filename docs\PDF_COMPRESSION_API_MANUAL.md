# PDF 壓縮 API 操作手冊

## 概述

本系統提供 PDF 壓縮 API，用於將推薦函 PDF 檔案直接打包成 ZIP 檔案，供外部系統下載使用。

### 主要變更

- **原本功能**：合併每位考生的推薦函 PDF 為單一檔案
- **新功能**：直接壓縮所有推薦函 PDF 檔案，保持原始檔案格式
- **檔案命名**：移除時間戳記，使用索引值.pdf 格式
- **覆蓋機制**：相同推薦人重複上傳時自動覆蓋舊檔案

## API 端點

### 基本資訊

- **基礎 URL**: `https://your-domain.com/api/pdf-merge/`
- **認證方式**: API 密鑰 + IP 白名單
- **回應格式**: JSON

### 1. 啟動壓縮任務

**端點**: `POST /api/pdf-merge/start-merge`

**請求參數**:

```json
{
    "exam_id": "2",
    "exam_year": 114
}
```

**回應範例**:

```json
{
    "status": "success",
    "message": "壓縮任務已啟動",
    "data": {
        "task_id": "task_67890abcdef",
        "status": "processing",
        "estimated_completion": "2024-01-15T10:30:00Z"
    }
}
```

### 2. 查詢任務狀態

**端點**: `GET /api/pdf-merge/merge-status?task_id={task_id}`

**回應範例**:

```json
{
    "status": "success",
    "data": {
        "task_id": "task_67890abcdef",
        "status": "ready",
        "progress": 100,
        "total_files": 25,
        "processed_files": 25,
        "download_url": "/api/pdf-merge/download?task_id=task_67890abcdef",
        "expires_at": "2024-01-16T10:30:00Z"
    }
}
```

**任務狀態說明**:

- `processing`: 處理中
- `ready`: 完成，可下載
- `failed`: 失敗
- `expired`: 已過期

### 3. 下載壓縮檔案

**端點**: `GET /api/pdf-merge/download?task_id={task_id}`

**回應**: ZIP 檔案下載

### 4. 公共下載（無需認證）

**端點**: `GET /api/public/pdf-download/{task_id}`

**說明**: 此端點無需 API 認證，適合外部系統直接下載

**回應**: ZIP 檔案下載

## CSV 參照檔格式

外部系統應準備 CSV 參照檔，格式如下：

### 欄位說明

```csv
"exam_id","stu_year(exam_year)","autono","stu_no","stu_name","dep_no","new_file_path"
```

### 欄位定義

| 欄位名稱            | 說明           | 範例               |
| ------------------- | -------------- | ------------------ |
| exam_id             | 招生代碼       | "2"                |
| stu_year(exam_year) | 招生年度       | "114"              |
| autono              | 考生報名流水號 | "349"              |
| stu_no              | 學號           | "12010001"         |
| stu_name            | 考生姓名       | "VＸ224001"        |
| dep_no              | 系所代碼       | "111"              |
| new_file_path       | 檔案路徑       | "111/12010001.pdf" |

### CSV 範例

```csv
"exam_id","stu_year(exam_year)","autono","stu_no","stu_name","dep_no","new_file_path"
"2","114","349","12010001","VＸ224001","111","111/12010001.pdf"
"2","114","350","12010002","VＸ224002","111","111/12010002.pdf"
"2","114","351","12010003","VＸ224003","112","112/12010003.pdf"
```

## ZIP 檔案結構

下載的 ZIP 檔案將包含以下結構：

```
merged_recommendations_exam_2_year_114_2024-01-15_10-30-00_task_67890abcdef.zip
├── README.txt                    # 說明檔案
├── 2/                           # exam_id 目錄
│   └── 114/                     # exam_year 目錄
│       ├── 349.pdf              # autono.pdf (第一個推薦函)
│       ├── 349_2.pdf            # autono_2.pdf (第二個推薦函，如果存在)
│       ├── 350.pdf
│       └── 351.pdf
```

### 檔案命名規則

- **單一推薦函**: `{autono}.pdf`
- **多個推薦函**: `{autono}_{index}.pdf`
- **目錄結構**: `{exam_id}/{exam_year}/`

## 測試指令

系統提供測試指令用於快速驗證功能：

### 列出可用選項

```bash
php artisan pdf:test-compression --list-available
```

### 乾跑測試

```bash
php artisan pdf:test-compression --exam_id=2 --exam_year=114 --dry-run
```

### 執行測試

```bash
php artisan pdf:test-compression --exam_id=2 --exam_year=114
```

## 外部下載測試

### 使用 wget 下載

```bash
# 公共下載（無需認證）
wget 'https://your-domain.com/api/public/pdf-download/{task_id}' -O downloaded_file.zip

# 範例
wget 'https://rec-letter.test/api/public/pdf-download/task_67890abcdef' -O test_download.zip
```

### 使用 curl 下載

```bash
# 公共下載（無需認證）
curl -L 'https://your-domain.com/api/public/pdf-download/{task_id}' -o downloaded_file.zip

# API 下載（需要認證）
curl -H 'X-API-Key: your-api-key' \
     'https://your-domain.com/api/pdf-merge/download?task_id={task_id}' \
     -o downloaded_file.zip

# 範例
curl -L 'https://rec-letter.test/api/public/pdf-download/task_67890abcdef' -o test_download.zip
```

### 測試完整流程

```bash
# 1. 啟動壓縮任務
TASK_RESPONSE=$(curl -X POST https://your-domain.com/api/pdf-merge/start-merge \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"exam_id": "2", "exam_year": 114}')

# 2. 提取 task_id
TASK_ID=$(echo $TASK_RESPONSE | jq -r '.data.task_id')

# 3. 等待任務完成
while true; do
  STATUS=$(curl -s "https://your-domain.com/api/pdf-merge/merge-status?task_id=$TASK_ID" \
    -H "X-API-Key: your-api-key" | jq -r '.data.status')

  if [ "$STATUS" = "ready" ]; then
    echo "任務完成，開始下載..."
    break
  elif [ "$STATUS" = "failed" ]; then
    echo "任務失敗"
    exit 1
  else
    echo "任務進行中，狀態: $STATUS"
    sleep 5
  fi
done

# 4. 下載檔案
curl -L "https://your-domain.com/api/public/pdf-download/$TASK_ID" -o final_download.zip
echo "下載完成: final_download.zip"
```

## 錯誤處理

### 常見錯誤碼

| 錯誤碼 | 說明           | 解決方案                       |
| ------ | -------------- | ------------------------------ |
| 422    | 參數驗證失敗   | 檢查 exam_id 和 exam_year 格式 |
| 404    | 沒有找到推薦函 | 確認指定條件下有已提交的推薦函 |
| 400    | 檔案尚未準備好 | 等待任務完成或檢查任務狀態     |
| 500    | 伺服器內部錯誤 | 聯繫系統管理員                 |

### 錯誤回應範例

```json
{
    "status": "error",
    "message": "沒有找到符合條件的已提交推薦函",
    "details": {
        "exam_id": "2",
        "exam_year": 114,
        "total_recommendations": 0,
        "applicants_with_recommendations": 0
    }
}
```

## 系統需求

### 伺服器需求

- PHP 8.1+
- Laravel 10+
- ZIP 擴展
- 足夠的磁碟空間用於臨時檔案

### 外部系統需求

- 支援 HTTP/HTTPS 請求
- JSON 解析能力
- ZIP 檔案解壓縮功能

## 安全考量

### API 安全

- 使用 API 密鑰認證
- IP 白名單限制
- HTTPS 傳輸加密

### 檔案安全

- 檔案存儲在私有目錄
- 下載連結有時效性（24小時）
- 自動清理過期檔案

## 監控與日誌

### 日誌記錄

系統會記錄以下事件：

- API 請求和回應
- 任務建立和完成
- 檔案壓縮過程
- 錯誤和異常

### 監控指標

建議監控：

- API 回應時間
- 任務成功率
- 磁碟使用量
- 記憶體使用量

## 實作範例

### PHP 範例

```php
<?php

class PdfCompressionApiClient
{
    private $baseUrl;
    private $apiKey;

    public function __construct($baseUrl, $apiKey)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;
    }

    public function startCompression($examId, $examYear)
    {
        $url = $this->baseUrl . '/api/pdf-merge/start-merge';
        $data = [
            'exam_id' => $examId,
            'exam_year' => $examYear
        ];

        $response = $this->makeRequest('POST', $url, $data);
        return json_decode($response, true);
    }

    public function checkStatus($taskId)
    {
        $url = $this->baseUrl . '/api/pdf-merge/merge-status?task_id=' . $taskId;
        $response = $this->makeRequest('GET', $url);
        return json_decode($response, true);
    }

    public function downloadFile($taskId, $savePath)
    {
        $url = $this->baseUrl . '/api/pdf-merge/download?task_id=' . $taskId;
        $response = $this->makeRequest('GET', $url);
        file_put_contents($savePath, $response);
        return $savePath;
    }

    private function makeRequest($method, $url, $data = null)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-API-Key: ' . $this->apiKey
        ]);

        if ($method === 'POST' && $data) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode >= 400) {
            throw new Exception("API request failed with HTTP {$httpCode}: {$response}");
        }

        return $response;
    }
}

// 使用範例
$client = new PdfCompressionApiClient('https://your-domain.com', 'your-api-key');

// 啟動壓縮
$result = $client->startCompression('2', 114);
$taskId = $result['data']['task_id'];

// 輪詢狀態
do {
    sleep(5);
    $status = $client->checkStatus($taskId);
} while ($status['data']['status'] === 'processing');

// 下載檔案
if ($status['data']['status'] === 'ready') {
    $client->downloadFile($taskId, '/path/to/save/file.zip');
}
?>
```

## 部署注意事項

### 環境設定

確保以下環境變數已正確設定：

```env
# API 安全設定
API_SECRET=your-secure-api-secret
API_WHITELIST_IPS=*************,*********

# 檔案存儲設定
FILESYSTEM_DISK=local
PDF_STORAGE_PATH=recommendations

# 任務設定
PDF_MERGE_TIMEOUT=1800
PDF_MERGE_EXPIRES_HOURS=24
```

### 定期維護

建議設定以下定期任務：

```bash
# 每日清理過期任務
0 2 * * * php /path/to/artisan pdf-merge:cleanup

# 每週檢查磁碟空間
0 3 * * 0 df -h /path/to/storage

# 每月備份重要日誌
0 4 1 * * tar -czf /backup/logs-$(date +%Y%m).tar.gz /path/to/logs
```

## 故障排除

### 1. 壓縮檔案無法建立

**問題**: 系統回報「無法創建ZIP檔案」

**解決方案**:

```bash
# 檢查存儲目錄權限
ls -la storage/app/public/
ls -la storage/app/public/pdf_merges/

# 設置正確權限
chmod -R 755 storage/app/public/
chmod -R 644 storage/app/public/pdf_merges/*

# 確保目錄存在
mkdir -p storage/app/public/pdf_merges

# 檢查磁碟空間
df -h storage/
```

### 2. 檔案不存在錯誤

**問題**: 下載時提示「檔案不存在」

**解決方案**:

```bash
# 檢查檔案是否存在
php artisan pdf:test-compression --list-available

# 檢查 storage link
ls -la public/storage
php artisan storage:link

# 檢查檔案路徑
find storage/app -name "*.zip" -type f
```

### 3. 外部訪問權限問題

**問題**: wget/curl 無法下載檔案

**解決方案**:

```bash
# 檢查 Web 服務器配置
# Apache: 確保 .htaccess 允許訪問
# Nginx: 確保 location 配置正確

# 檢查防火牆設置
sudo ufw status
sudo iptables -L

# 測試本地訪問
curl -I http://localhost/storage/pdf_merges/test.zip
```

### 4. 測試腳本

使用提供的測試腳本進行完整測試：

```bash
# 設置權限
chmod +x scripts/setup-storage-permissions.sh
./scripts/setup-storage-permissions.sh

# 執行測試
chmod +x scripts/test-pdf-compression.sh
./scripts/test-pdf-compression.sh https://your-domain.com your-api-key 2 114
```

## 聯絡資訊

如有問題或需要技術支援，請聯繫：

- **系統管理員**: [管理員聯絡方式]
- **技術支援**: [技術支援聯絡方式]
- **緊急聯絡**: [緊急聯絡方式]

---

**最後更新**: 2024-01-15
**版本**: 2.0
