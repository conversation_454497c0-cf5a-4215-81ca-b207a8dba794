<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;

/**
 * 外部郵件 API 服務類
 * 
 * 透過外部 API 發送郵件，同時保留 Blade 模板渲染功能
 */
class ExternalMailService
{
    /**
     * 外部 API 端點
     */
    private string $apiUrl;

    /**
     * API 認證帳號
     */
    private string $account;

    /**
     * API 認證密碼
     */
    private string $password;

    /**
     * 預設回覆信箱
     */
    private string $defaultReplyTo;

    public function __construct()
    {
        $this->apiUrl = config('mail.external_api.url', 'http://example.com/sendmail.php');
        $this->account = config('mail.external_api.account', 'account');
        $this->password = config('mail.external_api.password', 'password');
        $this->defaultReplyTo = config('mail.external_api.reply_to', config('mail.from.address'));
    }

    /**
     * 透過外部 API 發送郵件
     * 
     * @param string $subject 郵件主旨
     * @param string $content 郵件內容 (HTML)
     * @param string $recipient 收件人信箱
     * @param string|null $reply 回覆信箱
     * @return array 包含 success 和 message 的結果
     */
    public function sendMailService(string $subject, string $content, string $recipient, string $reply = null): array
    {
        try {
            $replyTo = $reply ?? $this->defaultReplyTo;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
                "subject" => $subject,
                "content" => $content,
                "recipient" => $recipient,
                "account" => $this->account,
                "password" => $this->password,
                "reply" => $replyTo
            ]));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // 30秒超時
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 跟隨重定向
            curl_setopt($ch, CURLOPT_USERAGENT, 'Laravel-RecommendationSystem/1.0'); // 設定 User Agent

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                throw new \Exception("cURL Error: " . $error);
            }

            if ($httpCode !== 200) {
                throw new \Exception("HTTP Error: " . $httpCode . " - " . $result);
            }

            Log::info('外部 API 郵件發送成功', [
                'recipient' => $recipient,
                'subject' => $subject,
                'api_response' => $result
            ]);

            return [
                'success' => true,
                'message' => '郵件發送成功',
                'api_response' => $result
            ];
        } catch (\Exception $e) {
            Log::error('外部 API 郵件發送失敗', [
                'recipient' => $recipient,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '郵件發送失敗: ' . $e->getMessage(),
                'api_response' => null
            ];
        }
    }

    /**
     * 使用 Blade 模板渲染郵件內容並發送
     * 
     * @param string $template Blade 模板名稱
     * @param array $data 模板資料
     * @param string $subject 郵件主旨
     * @param string $recipient 收件人信箱
     * @param string|null $reply 回覆信箱
     * @return array 發送結果
     */
    public function sendWithTemplate(string $template, array $data, string $subject, string $recipient, string $reply = null): array
    {
        try {
            // 使用 Blade 模板渲染郵件內容
            $content = View::make($template, $data)->render();

            // 透過外部 API 發送
            return $this->sendMailService($subject, $content, $recipient, $reply);
        } catch (\Exception $e) {
            Log::error('模板郵件發送失敗', [
                'template' => $template,
                'recipient' => $recipient,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '模板郵件發送失敗: ' . $e->getMessage(),
                'api_response' => null
            ];
        }
    }

    /**
     * 批量發送郵件
     * 
     * @param array $emails 郵件陣列，每個元素包含 subject, content, recipient, reply
     * @return array 發送結果統計
     */
    public function sendBatch(array $emails): array
    {
        $results = [
            'total' => count($emails),
            'success' => 0,
            'failed' => 0,
            'details' => []
        ];

        foreach ($emails as $index => $email) {
            $result = $this->sendMailService(
                $email['subject'],
                $email['content'],
                $email['recipient'],
                $email['reply'] ?? null
            );

            $results['details'][$index] = $result;

            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
            }
        }

        return $results;
    }

    /**
     * 測試外部 API 連線
     * 
     * @return array 測試結果
     */
    public function testConnection(): array
    {
        return $this->sendMailService(
            '測試郵件',
            '<h1>這是一封測試郵件</h1><p>如果您收到此郵件，表示外部 API 連線正常。</p>',
            $this->defaultReplyTo,
            $this->defaultReplyTo
        );
    }
}
