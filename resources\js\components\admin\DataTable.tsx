import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Download, FileText } from 'lucide-react';
import { useState } from 'react';

interface Column {
    key: string;
    label: string;
    sortable?: boolean;
    render?: (value: any, row: any) => React.ReactNode;
    className?: string;
}

interface Action {
    key: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (row: any) => void;
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
    className?: string;
    show?: (row: any) => boolean;
}

interface DataTableProps {
    data: any[];
    columns: Column[];
    actions?: Action[];
    title?: string;
    description?: string;
    emptyMessage?: string;
    pagination?: {
        enabled: boolean;
        pageSize?: number;
        showSizeSelector?: boolean;
    };
    exportable?: boolean;
    onExport?: () => void;
    className?: string;
}

/**
 * 通用資料表格元件
 *
 * 提供排序、分頁、操作按鈕等功能的資料表格
 *
 * @param {DataTableProps} props - 元件屬性
 * @param {any[]} props.data - 資料陣列
 * @param {Column[]} props.columns - 欄位定義
 * @param {Action[]} [props.actions] - 操作按鈕定義
 * @param {string} [props.title] - 表格標題
 * @param {string} [props.description] - 表格描述
 * @param {string} [props.emptyMessage] - 無資料時顯示的訊息
 * @param {Object} [props.pagination] - 分頁設定
 * @param {boolean} props.pagination.enabled - 是否啟用分頁
 * @param {number} [props.pagination.pageSize] - 每頁顯示的資料數量
 * @param {boolean} [props.exportable] - 是否啟用匯出功能
 * @param {function} [props.onExport] - 匯出資料的回調函數
 * @param {string} [props.className] - 附加的 CSS 類名
 */
export default function DataTable({
    data,
    columns,
    actions = [],
    title,
    description,
    emptyMessage = '沒有找到相關資料',
    pagination = { enabled: true, pageSize: 10 },
    exportable = false,
    onExport,
    className = '',
}: DataTableProps) {
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(pagination.pageSize || 10);
    const [sortColumn, setSortColumn] = useState<string | null>(null);
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

    // 排序邏輯
    const sortedData = [...data].sort((a, b) => {
        if (!sortColumn) return 0;

        const aValue = a[sortColumn];
        const bValue = b[sortColumn];

        if (aValue === bValue) return 0;

        const comparison = aValue < bValue ? -1 : 1;
        return sortDirection === 'asc' ? comparison : -comparison;
    });

    // 分頁邏輯
    const totalPages = Math.ceil(sortedData.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = pagination.enabled ? sortedData.slice(startIndex, endIndex) : sortedData;

    // 處理排序
    const handleSort = (columnKey: string) => {
        const column = columns.find((col) => col.key === columnKey);
        if (!column?.sortable) return;

        if (sortColumn === columnKey) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortColumn(columnKey);
            setSortDirection('asc');
        }
    };

    // 處理分頁
    const handlePageChange = (page: number) => {
        setCurrentPage(Math.max(1, Math.min(page, totalPages)));
    };

    const renderCellValue = (column: Column, row: any) => {
        const value = row[column.key];

        if (column.render) {
            return column.render(value, row);
        }

        return value;
    };

    return (
        <Card className={className}>
            {(title || description || exportable) && (
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            {title && <CardTitle>{title}</CardTitle>}
                            {description && <CardDescription>{description}</CardDescription>}
                        </div>
                        {exportable && onExport && (
                            <Button variant="outline" size="sm" onClick={onExport}>
                                <Download className="mr-2 h-4 w-4" />
                                匯出
                            </Button>
                        )}
                    </div>
                </CardHeader>
            )}

            <CardContent>
                {data.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                        <FileText className="mb-4 h-12 w-12 text-gray-400" />
                        <p className="text-gray-500">{emptyMessage}</p>
                    </div>
                ) : (
                    <>
                        {/* 表格 */}
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead>
                                    <tr className="border-b">
                                        {columns.map((column) => (
                                            <th
                                                key={column.key}
                                                className={`px-4 py-3 text-left font-medium text-gray-900 ${
                                                    column.sortable ? 'cursor-pointer hover:bg-gray-50' : ''
                                                } ${column.className || ''}`}
                                                onClick={() => handleSort(column.key)}
                                            >
                                                <div className="flex items-center gap-2">
                                                    {column.label}
                                                    {column.sortable && sortColumn === column.key && (
                                                        <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                                                    )}
                                                </div>
                                            </th>
                                        ))}
                                        {actions.length > 0 && <th className="px-4 py-3 text-left font-medium text-gray-900">操作</th>}
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedData.map((row, index) => (
                                        <tr key={index} className="border-b hover:bg-gray-50">
                                            {columns.map((column) => (
                                                <td key={column.key} className={`px-4 py-3 ${column.className || ''}`}>
                                                    {renderCellValue(column, row)}
                                                </td>
                                            ))}
                                            {actions.length > 0 && (
                                                <td className="px-4 py-3">
                                                    <div className="flex gap-2">
                                                        {actions.map((action) => {
                                                            if (action.show && !action.show(row)) {
                                                                return null;
                                                            }

                                                            return (
                                                                <Button
                                                                    key={action.key}
                                                                    variant={action.variant || 'outline'}
                                                                    size="sm"
                                                                    onClick={() => action.onClick(row)}
                                                                    className={action.className}
                                                                >
                                                                    {action.icon}
                                                                    {action.label}
                                                                </Button>
                                                            );
                                                        })}
                                                    </div>
                                                </td>
                                            )}
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>

                        {/* 分頁控制 */}
                        {pagination.enabled && totalPages > 1 && (
                            <div className="mt-4 flex items-center justify-between">
                                <div className="text-sm text-gray-500">
                                    顯示 {startIndex + 1} 到 {Math.min(endIndex, data.length)} 筆，共 {data.length} 筆資料
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(currentPage - 1)}
                                        disabled={currentPage === 1}
                                    >
                                        <ChevronLeft className="h-4 w-4" />
                                        上一頁
                                    </Button>
                                    <span className="text-sm">
                                        第 {currentPage} 頁，共 {totalPages} 頁
                                    </span>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handlePageChange(currentPage + 1)}
                                        disabled={currentPage === totalPages}
                                    >
                                        下一頁
                                        <ChevronRight className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </CardContent>
        </Card>
    );
}
