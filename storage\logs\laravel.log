[2025-07-25 14:23:13] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 14:23:13] local.INFO: PDF合併任務已啟動 {"task_id":"merge_1adY64VxU5crntMb_1753424593","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:23:13.242083Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 14:23:14] local.INFO: 開始處理PDF合併任務 {"task_id":"merge_nqzeii3fMzYGEkRt_1753424295","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:18:15.225735Z","client_ip":"127.0.0.1"}} 
[2025-07-25 14:23:14] local.INFO: 開始PDF合併處理 {"applicant_id":1,"file_count":2,"available_methods":["fpdi","tcpdf","dompdf"]} 
[2025-07-25 14:23:14] local.INFO: 嘗試使用FPDI合併 {"applicant_id":1,"method":"fpdi","file_count":2,"files":[{"path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","exists":true,"size":47140},{"path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","exists":true,"size":488789}]} 
[2025-07-25 14:23:14] local.INFO: FPDI合併開始 {"applicant_id":1,"file_count":2} 
[2025-07-25 14:23:14] local.INFO: 開始處理PDF檔案 {"applicant_id":1,"files_to_process":2} 
[2025-07-25 14:23:14] local.INFO: 處理PDF檔案 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","file_index":0,"file_size":47140} 
[2025-07-25 14:23:14] local.INFO: PDF檔案頁數 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","page_count":1} 
[2025-07-25 14:23:14] local.DEBUG: 導入PDF頁面 {"applicant_id":1,"file_index":0,"page_no":1,"total_pages":1,"page_size":{"width":209.90277777777774,"height":296.6861111111111,"0":209.90277777777774,"1":296.6861111111111,"orientation":"P"}} 
[2025-07-25 14:23:14] local.INFO: PDF檔案處理完成 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","pages_added":1,"total_pages_so_far":1} 
[2025-07-25 14:23:14] local.INFO: 處理PDF檔案 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","file_index":1,"file_size":488789} 
[2025-07-25 14:23:14] local.INFO: PDF檔案頁數 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","page_count":1} 
[2025-07-25 14:23:14] local.DEBUG: 導入PDF頁面 {"applicant_id":1,"file_index":1,"page_no":1,"total_pages":2,"page_size":{"width":211.66666666666666,"height":282.2222222222222,"0":211.66666666666666,"1":282.2222222222222,"orientation":"P"}} 
[2025-07-25 14:23:14] local.INFO: PDF檔案處理完成 {"applicant_id":1,"file_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","pages_added":1,"total_pages_so_far":2} 
[2025-07-25 14:23:14] local.INFO: 書籤添加完成 {"applicant_id":1,"bookmark_count":2} 
[2025-07-25 14:23:14] local.INFO: FPDI PDF合併成功完成 {"applicant_id":1,"autono":1,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","processed_files":2,"total_files":2,"total_pages":2,"bookmark_count":2,"final_file_size":539651} 
[2025-07-25 14:23:14] local.INFO: 使用FPDI合併成功完成 {"applicant_id":1,"method":"fpdi","result_file":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","result_size":539651} 
[2025-07-25 14:23:14] local.INFO: 考生PDF合併完成 {"applicant_id":1,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","file_count":2} 
[2025-07-25 14:23:14] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","zip_name":"348.pdf"} 
[2025-07-25 14:23:14] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-25 14:23:14] local.INFO: ZIP檔案創建成功 {"zip_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_14-23-14_merge_nqzeii3fMzYGEkRt_1753424295.zip","added_files":1,"total_files":1,"task_id":"merge_nqzeii3fMzYGEkRt_1753424295"} 
[2025-07-25 14:23:14] local.INFO: PDF合併任務完成 {"task_id":"merge_nqzeii3fMzYGEkRt_1753424295","merged_files":1,"zip_file":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_14-23-14_merge_nqzeii3fMzYGEkRt_1753424295.zip"} 
[2025-07-25 14:23:14] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 14:23:42] local.INFO: 管理員取消PDF合併任務 {"task_id":"merge_1adY64VxU5crntMb_1753424593","user_id":1} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:29:24] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:29:24.456480Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:35:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 14:35:47] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 14:35:47] local.INFO: PDF合併檔案下載 {"task_id":"merge_nqzeii3fMzYGEkRt_1753424295","file_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_14-23-14_merge_nqzeii3fMzYGEkRt_1753424295.zip","client_ip":"127.0.0.1"} 
[2025-07-25 14:39:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 14:39:32] local.INFO: PDF合併檔案下載 {"task_id":"merge_nqzeii3fMzYGEkRt_1753424295","file_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_14-23-14_merge_nqzeii3fMzYGEkRt_1753424295.zip","client_ip":"127.0.0.1"} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:43:35] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:43:35.265163Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:44:20] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-25 14:44:38] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T06:44:37.975322Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T06:44:37.975720Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T06:44:37.975791Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T06:44:37.975854Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T06:44:37.975914Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T06:44:37.975981Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T06:44:37.976080Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T06:44:37.976159Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T06:44:37.976216Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T06:44:37.976273Z"}]} 
[2025-07-25 14:44:39] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T06:44:37.975322Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T06:44:37.975720Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T06:44:37.975791Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T06:44:37.975854Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T06:44:37.975914Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T06:44:37.975981Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T06:44:37.976080Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T06:44:37.976159Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T06:44:37.976216Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T06:44:37.976273Z"}]}}}} 
[2025-07-25 14:45:08] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 14:45:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 14:45:14] local.INFO: 建立新使用者帳號 {"user_id":2,"email":"<EMAIL>","role":"applicant"} 
[2025-07-25 14:45:14] local.INFO: 建立新考生記錄 {"user_id":2,"applicant_id":1,"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":"114","exam_id":"2"} 
[2025-07-25 14:45:14] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 14:45:18] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 14:45:29] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:45:29.877230Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:45:29] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-25 14:45:29] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"吳宗霖","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-25 14:45:29] local.INFO: 建立新的推薦人使用者帳號 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖"} 
[2025-07-25 14:45:29] local.INFO: 嘗試新增推薦人資料 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-25 14:45:29] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753425929} 
[2025-07-25 14:45:29] local.INFO: 建立新的推薦人資料 {"recommender_id":1,"user_id":3,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-25 14:45:30] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:45:30] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":1,"recommender_email":"<EMAIL>","email_log_id":1} 
[2025-07-25 14:45:38] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:45:38.446979Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:45:38] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-25 14:45:38] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"資訊工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":349}} 
[2025-07-25 14:45:38] local.INFO: 建立新的推薦人使用者帳號 {"user_id":4,"email":"<EMAIL>","name":"Mike"} 
[2025-07-25 14:45:38] local.INFO: 嘗試新增推薦人資料 {"user_id":4,"email":"<EMAIL>","name":"Mike","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-25 14:45:38] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753425938} 
[2025-07-25 14:45:38] local.INFO: 建立新的推薦人資料 {"recommender_id":2,"user_id":4,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-25 14:45:38] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:45:38] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":2,"recommender_email":"<EMAIL>","email_log_id":2} 
[2025-07-25 14:45:52] local.INFO: 推薦人登入成功 {"recommender_id":1,"user_id":3,"token":"9331b3c7dfced25ffefce5f39525eae80655abd3127ce37df62236638b45a5c7","ip":"127.0.0.1"} 
[2025-07-25 14:46:10] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:46:42] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:46:42.807969Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:46:42] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":3} 
[2025-07-25 14:46:42] local.INFO: PDF檔案已存儲 {"recommendation_id":1,"file_path":"recommendations/2/114/348/1_2025-07-25_14-46-42.pdf","file_size":973,"original_name":"1.pdf"} 
[2025-07-25 14:46:42] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"1","file_path":"recommendations/2/114/348/1_2025-07-25_14-46-42.pdf","file_size":973} 
[2025-07-25 14:46:43] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:46:43] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":1,"applicant_email":"<EMAIL>","email_log_id":3} 
[2025-07-25 14:46:43] local.INFO: 推薦函提交成功 {"recommendation_id":"1","submission_type":"pdf","user_id":3} 
[2025-07-25 14:46:45] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:46:52] local.INFO: 推薦人登入成功 {"recommender_id":2,"user_id":4,"token":"5736cb622e22682f1cfe00dcd7ecad38b449eb95aa3d2da8f77a1728de828f39","ip":"127.0.0.1"} 
[2025-07-25 14:47:07] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:47:07.990498Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:47:07] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-25 14:47:08] local.INFO: PDF檔案已存儲 {"recommendation_id":2,"file_path":"recommendations/2/114/349/2_2025-07-25_14-47-08.pdf","file_size":488789,"original_name":"Group 1.pdf"} 
[2025-07-25 14:47:08] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"2","file_path":"recommendations/2/114/349/2_2025-07-25_14-47-08.pdf","file_size":488789} 
[2025-07-25 14:47:08] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:47:08] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":2,"applicant_email":"<EMAIL>","email_log_id":4} 
[2025-07-25 14:47:08] local.INFO: 推薦函提交成功 {"recommendation_id":"2","submission_type":"pdf","user_id":4} 
[2025-07-25 14:48:38] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:48:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 14:48:57] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 14:48:58] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 14:49:03] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 14:49:12] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:49:12.074662Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:49:12] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-25 14:49:12] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-25 14:49:12] local.INFO: 更新推薦人考試資訊 {"recommender_id":2,"updated_fields":["exam_year","exam_id"]} 
[2025-07-25 14:49:12] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:49:12] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":3,"recommender_email":"<EMAIL>","email_log_id":5} 
[2025-07-25 14:49:26] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:49:26.674177Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:49:26] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-25 14:49:26] local.INFO: PDF檔案已存儲 {"recommendation_id":3,"file_path":"recommendations/2/114/348/2_2025-07-25_14-49-26.pdf","file_size":488789,"original_name":"Group 1.pdf"} 
[2025-07-25 14:49:26] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"3","file_path":"recommendations/2/114/348/2_2025-07-25_14-49-26.pdf","file_size":488789} 
[2025-07-25 14:49:27] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 14:49:27] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":3,"applicant_email":"<EMAIL>","email_log_id":6} 
[2025-07-25 14:49:27] local.INFO: 推薦函提交成功 {"recommendation_id":"3","submission_type":"pdf","user_id":4} 
[2025-07-25 14:49:44] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:20] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:50:20.441605Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:50:29] local.INFO: 管理員創建PDF合併任務 {"task_id":"merge_Keg8QfkvaFy3cJ4o_1753426229","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:50:29.627982Z","client_ip":"127.0.0.1","created_by":"admin","user_id":1},"user_id":1} 
[2025-07-25 14:52:12] local.INFO: 管理員刪除PDF合併任務 {"task_id":"merge_Keg8QfkvaFy3cJ4o_1753426229","user_id":1} 
[2025-07-25 14:52:30] local.INFO: 管理員創建PDF合併任務 {"task_id":"merge_BoJxEE6HzPgYoBSL_1753426350","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:52:30.402350Z","client_ip":"127.0.0.1","created_by":"admin","user_id":1},"user_id":1} 
[2025-07-25 14:52:35] local.INFO: 管理員取消PDF合併任務 {"task_id":"merge_BoJxEE6HzPgYoBSL_1753426350","user_id":1} 
[2025-07-25 14:52:42] local.INFO: 管理員刪除PDF合併任務 {"task_id":"merge_BoJxEE6HzPgYoBSL_1753426350","user_id":1} 
[2025-07-25 14:53:36] local.INFO: 管理員創建PDF合併任務 {"task_id":"merge_WkE6860j2eH2zZjY_1753426416","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T06:53:36.894456Z","client_ip":"127.0.0.1","created_by":"admin","user_id":1},"user_id":1} 
[2025-07-25 14:53:45] local.INFO: 管理員刪除PDF合併任務 {"task_id":"merge_WkE6860j2eH2zZjY_1753426416","user_id":1} 
[2025-07-25 14:54:38] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:54:39] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:54:38.992943Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:07:35] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:07:35.500826Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:16:24] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:16:24.761547Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:24:38] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:24:38.331377Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:31:04] local.ERROR: App\Http\Controllers\Admin\EmailLogController::show(): Argument #1 ($id) must be of type int, string given, called in C:\Users\<USER>\Desktop\rec-letter\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php on line 46 {"userId":1,"exception":"[object] (TypeError(code: 0): App\\Http\\Controllers\\Admin\\EmailLogController::show(): Argument #1 ($id) must be of type int, string given, called in C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php on line 46 at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Admin\\EmailLogController.php:49)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\EmailLogController->show('export')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\EmailLogController), 'show')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckSystemAccess.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckSystemAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckUserRole.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleAppearance.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#60 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#61 {main}
"} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:00] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:32:00.220768Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 15:32:32] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T07:32:32.579178Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 10:17:06] local.INFO: 管理員創建PDF合併任務 {"task_id":"merge_J4yBL4ePrGaxXujk_1753669025","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-28T02:17:05.824640Z","client_ip":"127.0.0.1","created_by":"admin","user_id":1},"user_id":1} 
[2025-07-28 10:17:30] local.INFO: 管理員刪除PDF合併任務 {"task_id":"merge_J4yBL4ePrGaxXujk_1753669025","user_id":1} 
[2025-07-28 10:17:56] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 10:18:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 13:02:33] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-28 13:02:40] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-28 13:02:46] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-28 13:11:18] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:11:18] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:11:18] local.ERROR: The file "C:\Users\<USER>\Desktop\rec-letter\storage\app/recommendations/2/114/348/1.pdf" does not exist {"exception":"[object] (Symfony\\Component\\HttpFoundation\\File\\Exception\\FileNotFoundException(code: 0): The file \"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/348/1.pdf\" does not exist at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\File\\File.php:36)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php(73): Symfony\\Component\\HttpFoundation\\File\\File->__construct('C:\\\\Users\\\\<USER>\\\\D...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\http-foundation\\BinaryFileResponse.php(50): Symfony\\Component\\HttpFoundation\\BinaryFileResponse->setFile('C:\\\\Users\\\\<USER>\\\\D...', NULL, false, true)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(297): Symfony\\Component\\HttpFoundation\\BinaryFileResponse->__construct('C:\\\\Users\\\\<USER>\\\\D...', 200, Array)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php(390): Illuminate\\Routing\\ResponseFactory->file('C:\\\\Users\\\\<USER>\\\\D...', Array)
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->download('2', '114', '348', '1')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'download')
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#43 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#44 {main}
"} 
[2025-07-28 13:13:36] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:13:36] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:13:36] local.ERROR: Undefined variable $year {"exception":"[object] (ErrorException(code: 0): Undefined variable $year at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:382)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\\\D...', 382)
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php(382): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Undefined varia...', 'C:\\\\Users\\\\<USER>\\\\D...', 382)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->download('2', '114', '348', '1')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'download')
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#41 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#42 {main}
"} 
[2025-07-28 13:13:42] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:13:42] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:13:48] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:13:49] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:16:47] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:16:48] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:16:48] local.WARNING: 推薦函檔案不存在：private/recommendations/2/114/348/1.pdf  
[2025-07-28 13:17:38] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/1","method":"GET"} 
[2025-07-28 13:17:38] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"1"} 
[2025-07-28 13:17:59] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download-recommendation/2/114/348/2","method":"GET"} 
[2025-07-28 13:17:59] local.INFO: 下載推薦函請求 {"exam_id":"2","exam_year":"114","autono":"348","index":"2"} 
[2025-07-28 14:16:02] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_PrdRkuumQRrNXcAQ_1753683361","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T06:16:01.753240Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 14:16:03] local.WARNING: PDF檔案不存在 {"recommendation_id":1,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/348/1_2025-07-25_14-46-42.pdf"} 
[2025-07-28 14:16:03] local.WARNING: PDF檔案不存在 {"recommendation_id":2,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/349/2_2025-07-25_14-47-08.pdf"} 
[2025-07-28 14:16:03] local.WARNING: PDF檔案不存在 {"recommendation_id":3,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/348/2_2025-07-25_14-49-26.pdf"} 
[2025-07-28 14:16:03] local.ERROR: PDF合併任務失敗 {"task_id":"merge_PrdRkuumQRrNXcAQ_1753683361","error":"沒有找到任何可壓縮的PDF檔案","trace":"#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\TestPdfCompressionCommand.php(229): App\\Jobs\\ProcessPdfMergeJob->handle()
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\TestPdfCompressionCommand.php(89): App\\Console\\Commands\\TestPdfCompressionCommand->executeCompressionTest('2', '114')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestPdfCompressionCommand->handle()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestPdfCompressionCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#16 {main}"} 
[2025-07-28 14:21:58] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-28 14:22:14] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-28T06:22:13.817850Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-28T06:22:13.818525Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-28T06:22:13.818676Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-28T06:22:13.818817Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-28T06:22:13.818938Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-28T06:22:13.819046Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-28T06:22:13.819243Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-28T06:22:13.819352Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-28T06:22:13.819456Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-28T06:22:13.819559Z"}]} 
[2025-07-28 14:22:15] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-28T06:22:13.817850Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-28T06:22:13.818525Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-28T06:22:13.818676Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-28T06:22:13.818817Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-28T06:22:13.818938Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-28T06:22:13.819046Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-28T06:22:13.819243Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-28T06:22:13.819352Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-28T06:22:13.819456Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-28T06:22:13.819559Z"}]}}}} 
[2025-07-28 14:22:26] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-28 14:22:32] local.INFO: 建立新使用者帳號 {"user_id":2,"email":"<EMAIL>","role":"applicant"} 
[2025-07-28 14:22:32] local.INFO: 建立新考生記錄 {"user_id":2,"applicant_id":1,"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":"114","exam_id":"2"} 
[2025-07-28 14:22:32] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-28 14:22:44] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T06:22:44.674250Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 14:22:44] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-28 14:22:44] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"吳宗霖","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-28 14:22:44] local.INFO: 建立新的推薦人使用者帳號 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖"} 
[2025-07-28 14:22:44] local.INFO: 嘗試新增推薦人資料 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-28 14:22:44] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753683764} 
[2025-07-28 14:22:44] local.INFO: 建立新的推薦人資料 {"recommender_id":1,"user_id":3,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-28 14:22:45] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-28 14:22:45] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":1,"recommender_email":"<EMAIL>","email_log_id":1} 
[2025-07-28 14:22:50] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T06:22:50.346276Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 14:22:50] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-28 14:22:50] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"資訊工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":349}} 
[2025-07-28 14:22:50] local.INFO: 建立新的推薦人使用者帳號 {"user_id":4,"email":"<EMAIL>","name":"Mike"} 
[2025-07-28 14:22:50] local.INFO: 嘗試新增推薦人資料 {"user_id":4,"email":"<EMAIL>","name":"Mike","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-28 14:22:50] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753683770} 
[2025-07-28 14:22:50] local.INFO: 建立新的推薦人資料 {"recommender_id":2,"user_id":4,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-28 14:22:50] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-28 14:22:50] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":2,"recommender_email":"<EMAIL>","email_log_id":2} 
[2025-07-28 14:23:29] local.INFO: 推薦人登入成功 {"recommender_id":2,"user_id":4,"token":"67f5d97837638a139f1442dcbbff621ff46e10064b6d044569171bad2fa9ddc6","ip":"127.0.0.1"} 
[2025-07-28 14:23:44] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T06:23:44.401077Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 14:23:44] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-28 14:23:44] local.INFO: PDF檔案已存儲 {"recommendation_id":2,"file_path":"recommendations/2/114/349/2.pdf","file_size":488789,"original_name":"Group 1.pdf","is_overwrite":false,"recommender_id":2} 
[2025-07-28 14:23:44] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"2","file_path":"recommendations/2/114/349/2.pdf","file_size":488789} 
[2025-07-28 14:23:44] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（資訊工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-28 14:23:44] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":2,"applicant_email":"<EMAIL>","email_log_id":3} 
[2025-07-28 14:23:44] local.INFO: 推薦函提交成功 {"recommendation_id":"2","submission_type":"pdf","user_id":4} 
[2025-07-28 14:23:46] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-28 14:24:59] local.INFO: 推薦人登入成功 {"recommender_id":1,"user_id":3,"token":"80663c61361c64697e9e14943423a65ece9728944b50dc741595f66438031477","ip":"127.0.0.1"} 
[2025-07-28 14:25:10] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T06:25:10.712045Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 14:25:10] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":3} 
[2025-07-28 14:25:10] local.INFO: PDF檔案已存儲 {"recommendation_id":1,"file_path":"recommendations/2/114/348/1.pdf","file_size":47140,"original_name":"下學期課表.pdf","is_overwrite":true,"recommender_id":1} 
[2025-07-28 14:25:10] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"1","file_path":"recommendations/2/114/348/1.pdf","file_size":47140} 
[2025-07-28 14:25:11] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-28 14:25:11] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":1,"applicant_email":"<EMAIL>","email_log_id":4} 
[2025-07-28 14:25:11] local.INFO: 推薦函提交成功 {"recommendation_id":"1","submission_type":"pdf","user_id":3} 
[2025-07-28 14:25:12] local.ERROR: PDF預覽失敗 {"user_id":3,"file_path":"recommendations/2/114/348/1.pdf","error":"無權限訪問此檔案"} 
[2025-07-28 14:25:31] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_gk12MwW9efV0MwBv_1753683931","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T06:25:31.500859Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 14:25:31] local.WARNING: PDF檔案不存在 {"recommendation_id":1,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/348/1.pdf"} 
[2025-07-28 14:25:31] local.WARNING: PDF檔案不存在 {"recommendation_id":2,"pdf_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/recommendations/2/114/349/2.pdf"} 
[2025-07-28 14:25:31] local.ERROR: PDF合併任務失敗 {"task_id":"merge_gk12MwW9efV0MwBv_1753683931","error":"沒有找到任何可壓縮的PDF檔案","trace":"#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\TestPdfCompressionCommand.php(229): App\\Jobs\\ProcessPdfMergeJob->handle()
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Console\\Commands\\TestPdfCompressionCommand.php(89): App\\Console\\Commands\\TestPdfCompressionCommand->executeCompressionTest('2', '114')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\TestPdfCompressionCommand->handle()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\TestPdfCompressionCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#16 {main}"} 
[2025-07-28 14:40:37] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_g0cubhzt6UaPU8P8_1753684837","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T06:40:37.262514Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 14:40:38] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"2/114/348.pdf","file_size":47140} 
[2025-07-28 14:40:38] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"2/114/348_2.pdf","file_size":488789} 
[2025-07-28 14:40:38] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 14:40:38] local.INFO: ZIP檔案創建成功 {"task_id":"merge_g0cubhzt6UaPU8P8_1753684837","zip_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-28_14-40-38_merge_g0cubhzt6UaPU8P8_1753684837.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/merged_recommendations_exam_2_year_114_2025-07-28_14-40-38_merge_g0cubhzt6UaPU8P8_1753684837.zip","file_count":2,"file_size":499858} 
[2025-07-28 14:40:38] local.INFO: PDF壓縮任務完成 {"task_id":"merge_g0cubhzt6UaPU8P8_1753684837","compressed_files":2,"zip_file":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-28_14-40-38_merge_g0cubhzt6UaPU8P8_1753684837.zip"} 
[2025-07-28 14:50:02] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_mJpz3qwIH2BomHVj_1753685402","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T06:50:02.897850Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 14:50:02] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 14:50:02] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 14:50:02] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 14:50:03] local.INFO: ZIP檔案創建成功 {"task_id":"merge_mJpz3qwIH2BomHVj_1753685402","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_14-50-02.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_14-50-02.zip","file_count":2,"file_size":499711} 
[2025-07-28 14:50:03] local.INFO: PDF壓縮任務完成 {"task_id":"merge_mJpz3qwIH2BomHVj_1753685402","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_14-50-02.zip"} 
[2025-07-28 15:02:13] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_I9MAt9lzZzbDxGrh_1753686133","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:02:13.708093Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 15:02:13] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:02:13] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:02:13] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:02:13] local.INFO: ZIP檔案創建成功 {"task_id":"merge_I9MAt9lzZzbDxGrh_1753686133","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-02-13.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-02-13.zip","file_count":2,"file_size":499711} 
[2025-07-28 15:02:13] local.INFO: PDF壓縮任務完成 {"task_id":"merge_I9MAt9lzZzbDxGrh_1753686133","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-02-13.zip"} 
[2025-07-28 15:07:19] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_5tvLsvYjzQ6Lnpc4_1753686439","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:07:19.074155Z","client_ip":"127.0.0.1","created_by":"test_command","test_mode":true}} 
[2025-07-28 15:07:19] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:07:19] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:07:19] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:07:19] local.INFO: ZIP檔案創建成功 {"task_id":"merge_5tvLsvYjzQ6Lnpc4_1753686439","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-07-19.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-07-19.zip","file_count":2,"file_size":499711} 
[2025-07-28 15:07:19] local.INFO: PDF壓縮任務完成 {"task_id":"merge_5tvLsvYjzQ6Lnpc4_1753686439","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-07-19.zip"} 
[2025-07-28 15:08:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-28 15:20:17] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:20:17] local.INFO: PDF合併任務已啟動 {"task_id":"merge_y8VPCMCfQOorXj9w_1753687217","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:20:17.687206Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:20:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:20:28] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:20:34] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:20:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:20:45] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:21:57] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-28T07:21:57.874442Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-28 15:26:16] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_y8VPCMCfQOorXj9w_1753687217","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:20:17.687206Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:26:17] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:26:17] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:26:17] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:26:17] local.INFO: ZIP檔案創建成功 {"task_id":"merge_y8VPCMCfQOorXj9w_1753687217","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-26-17.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-26-17.zip","file_count":2,"file_size":499711} 
[2025-07-28 15:26:17] local.INFO: PDF壓縮任務完成 {"task_id":"merge_y8VPCMCfQOorXj9w_1753687217","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-26-17.zip"} 
[2025-07-28 15:29:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:29:33] local.INFO: PDF合併任務已啟動 {"task_id":"merge_hBRseTZfuX0NDrLQ_1753687773","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:29:33.032770Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:29:34] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_hBRseTZfuX0NDrLQ_1753687773","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:29:33.032770Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:29:34] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:29:34] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:29:34] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:29:34] local.INFO: ZIP檔案創建成功 {"task_id":"merge_hBRseTZfuX0NDrLQ_1753687773","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-29-34.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-29-34.zip","file_count":2,"file_size":499710} 
[2025-07-28 15:29:34] local.INFO: PDF壓縮任務完成 {"task_id":"merge_hBRseTZfuX0NDrLQ_1753687773","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-29-34.zip"} 
[2025-07-28 15:29:38] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:29:45] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:29:50] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:29:56] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:01] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:07] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:18] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:30:29] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:33:06] local.ERROR: syntax error, unexpected token "catch", expecting "function" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"catch\", expecting \"function\" at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:202)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Users\\\\<USER>\\\\D...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(46): method_exists('App\\\\Http\\\\Contro...', 'startMerge')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(26): Illuminate\\Routing\\RouteSignatureParameters::fromClassMethodString('App\\\\Http\\\\Contro...')
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(545): Illuminate\\Routing\\RouteSignatureParameters::fromAction(Array, Array)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(173): Illuminate\\Routing\\Route->signatureParameters(Array)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(122): Tighten\\Ziggy\\Ziggy->resolveBindings(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(32): Tighten\\Ziggy\\Ziggy->nameKeyedRoutes()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleInertiaRequests.php(50): Tighten\\Ziggy\\Ziggy->__construct()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Middleware\\HandleInertiaRequests->App\\Http\\Middleware\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Container\\Container->call(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(180): Illuminate\\Support\\Facades\\Facade::__callStatic('call', Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(135): Inertia\\Response->resolveArrayableProperties(Array, Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(106): Inertia\\Response->resolveProperties(Object(Illuminate\\Http\\Request), Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckSystemAccess.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckSystemAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckUserRole.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin')
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleAppearance.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#77 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#78 {main}
"} 

[2025-07-28 15:33:12] local.ERROR: syntax error, unexpected token "catch", expecting "function" {"userId":1,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"catch\", expecting \"function\" at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:208)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Users\\\\<USER>\\\\D...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(46): method_exists('App\\\\Http\\\\Contro...', 'startMerge')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(26): Illuminate\\Routing\\RouteSignatureParameters::fromClassMethodString('App\\\\Http\\\\Contro...')
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(545): Illuminate\\Routing\\RouteSignatureParameters::fromAction(Array, Array)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(173): Illuminate\\Routing\\Route->signatureParameters(Array)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(122): Tighten\\Ziggy\\Ziggy->resolveBindings(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(32): Tighten\\Ziggy\\Ziggy->nameKeyedRoutes()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleInertiaRequests.php(50): Tighten\\Ziggy\\Ziggy->__construct()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Middleware\\HandleInertiaRequests->App\\Http\\Middleware\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Container\\Container->call(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(180): Illuminate\\Support\\Facades\\Facade::__callStatic('call', Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(135): Inertia\\Response->resolveArrayableProperties(Array, Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(106): Inertia\\Response->resolveProperties(Object(Illuminate\\Http\\Request), Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckSystemAccess.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckSystemAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckUserRole.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserRole->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin')
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleAppearance.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#77 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#78 {main}
"} 
[2025-07-28 15:33:12] local.ERROR: syntax error, unexpected token "catch", expecting "function" {"userId":2,"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"catch\", expecting \"function\" at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:208)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\Users\\\\<USER>\\\\D...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(46): method_exists('App\\\\Http\\\\Contro...', 'startMerge')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteSignatureParameters.php(26): Illuminate\\Routing\\RouteSignatureParameters::fromClassMethodString('App\\\\Http\\\\Contro...')
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(545): Illuminate\\Routing\\RouteSignatureParameters::fromAction(Array, Array)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(173): Illuminate\\Routing\\Route->signatureParameters(Array)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(122): Tighten\\Ziggy\\Ziggy->resolveBindings(Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\tightenco\\ziggy\\src\\Ziggy.php(32): Tighten\\Ziggy\\Ziggy->nameKeyedRoutes()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleInertiaRequests.php(50): Tighten\\Ziggy\\Ziggy->__construct()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Middleware\\HandleInertiaRequests->App\\Http\\Middleware\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Container\\Container->call(Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(180): Illuminate\\Support\\Facades\\Facade::__callStatic('call', Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(135): Inertia\\Response->resolveArrayableProperties(Array, Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Response.php(106): Inertia\\Response->resolveProperties(Object(Illuminate\\Http\\Request), Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Inertia\\Response->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(891): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Inertia\\Response))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckSystemAccess.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckSystemAccess->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\CheckUserAgreement.php(55): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserAgreement->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\HandleAppearance.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleAppearance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#51 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#73 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#74 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#75 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#76 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#77 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#78 {main}
"} 
[2025-07-28 15:34:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:34:12] local.INFO: PDF合併任務已啟動 {"task_id":"merge_LHJw0z2Pv80kKifW_1753688052","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:34:12.698259Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:34:18] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:34:18] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"input":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:18] local.WARNING: ⚠️ 任務狀態查詢請求未提供 task_id {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:24] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:34:24] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"input":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:24] local.WARNING: ⚠️ 任務狀態查詢請求未提供 task_id {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:29] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:34:29] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"input":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:34:29] local.WARNING: ⚠️ 任務狀態查詢請求未提供 task_id {"query":{"taskId":"merge_LHJw0z2Pv80kKifW_1753688052"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:35:23] local.INFO: PDF合併任務已啟動 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:35:23.241548Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:35:29] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:29] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:29] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:35] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:35] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:35] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:40] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:40] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:40] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:46] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:46] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:46] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:52] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:52] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:35:57] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:35:57] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:35:57] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:36:03] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:36:03] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:36:03] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:36:08] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:36:08] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:36:08] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:36:14] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:36:14] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:36:14] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:36:20] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:36:20] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"input":{"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123"},"ip":"127.0.0.1"} 
[2025-07-28 15:36:20] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:39:23] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_LHJw0z2Pv80kKifW_1753688052","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:34:12.698259Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:39:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:39:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:39:23] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:39:23] local.INFO: ZIP檔案創建成功 {"task_id":"merge_LHJw0z2Pv80kKifW_1753688052","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip","file_count":2,"file_size":499709} 
[2025-07-28 15:39:23] local.INFO: PDF壓縮任務完成 {"task_id":"merge_LHJw0z2Pv80kKifW_1753688052","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip"} 
[2025-07-28 15:39:23] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:35:23.241548Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:39:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:39:23] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:39:23] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:39:23] local.INFO: ZIP檔案創建成功 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip","file_count":2,"file_size":499709} 
[2025-07-28 15:39:23] local.INFO: PDF壓縮任務完成 {"task_id":"merge_Pt3iJhbHqkhJTjIe_1753688123","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-39-23.zip"} 
[2025-07-28 15:41:31] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-28 15:41:31] local.INFO: PDF合併任務已啟動 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:41:31.863329Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-28 15:41:37] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:37] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:37] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:41:42] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:42] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:42] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"processing","ip":"127.0.0.1"} 
[2025-07-28 15:41:47] local.INFO: 開始處理PDF壓縮任務 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","parameters":{"exam_id":"2","exam_year":"114","requested_at":"2025-07-28T07:41:31.863329Z","client_ip":"127.0.0.1"}} 
[2025-07-28 15:41:47] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/348/1.pdf","zip_path":"348/1.pdf","file_size":47140} 
[2025-07-28 15:41:47] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/private\\recommendations/2/114/349/2.pdf","zip_path":"348/2.pdf","file_size":488789} 
[2025-07-28 15:41:47] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-28 15:41:47] local.INFO: ZIP檔案創建成功 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","zip_path":"pdf_merges/recommendations_2_114_2025-07-28_15-41-47.zip","full_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/public\\pdf_merges/recommendations_2_114_2025-07-28_15-41-47.zip","file_count":2,"file_size":499710} 
[2025-07-28 15:41:47] local.INFO: PDF壓縮任務完成 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","compressed_files":2,"zip_file":"pdf_merges/recommendations_2_114_2025-07-28_15-41-47.zip"} 
[2025-07-28 15:41:48] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:48] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:48] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:41:53] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:53] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:53] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:41:59] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:41:59] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:41:59] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:04] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:04] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:04] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:10] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:10] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:10] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:16] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:16] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:16] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:21] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:21] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:21] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
[2025-07-28 15:42:27] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-28 15:42:27] local.INFO: 📥 收到任務狀態查詢請求 {"query":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"input":{"task_id":"merge_mQKeci3uPltVSAtC_1753688491"},"ip":"127.0.0.1"} 
[2025-07-28 15:42:27] local.INFO: ✅ 回傳任務狀態 {"task_id":"merge_mQKeci3uPltVSAtC_1753688491","status":"error","ip":"127.0.0.1"} 
