#!/bin/bash

# PDF 壓縮系統存儲權限設置腳本

echo "=== PDF 壓縮系統存儲權限設置 ==="

# 獲取項目根目錄
PROJECT_ROOT=$(dirname $(dirname $(realpath $0)))
echo "項目根目錄: $PROJECT_ROOT"

# 設置存儲目錄權限
echo "設置存儲目錄權限..."

# 確保 storage 目錄存在
mkdir -p "$PROJECT_ROOT/storage/app/public/pdf_merges"
mkdir -p "$PROJECT_ROOT/storage/app/private/recommendations"
mkdir -p "$PROJECT_ROOT/storage/logs"
mkdir -p "$PROJECT_ROOT/storage/framework/cache"
mkdir -p "$PROJECT_ROOT/storage/framework/sessions"
mkdir -p "$PROJECT_ROOT/storage/framework/views"

# 設置權限
chmod -R 755 "$PROJECT_ROOT/storage"
chmod -R 755 "$PROJECT_ROOT/bootstrap/cache"

# 設置 PDF 合併目錄權限（公共訪問）
chmod -R 755 "$PROJECT_ROOT/storage/app/public"
chmod -R 644 "$PROJECT_ROOT/storage/app/public/pdf_merges"/* 2>/dev/null || true

# 設置推薦函目錄權限（私有訪問）
chmod -R 750 "$PROJECT_ROOT/storage/app/private"

echo "權限設置完成"

# 檢查 storage link
echo "檢查 storage link..."
if [ ! -L "$PROJECT_ROOT/public/storage" ]; then
    echo "創建 storage link..."
    cd "$PROJECT_ROOT"
    php artisan storage:link
else
    echo "storage link 已存在"
fi

# 顯示目錄結構
echo ""
echo "=== 存儲目錄結構 ==="
tree "$PROJECT_ROOT/storage/app" -L 3 2>/dev/null || find "$PROJECT_ROOT/storage/app" -type d | head -20

echo ""
echo "=== 權限檢查 ==="
ls -la "$PROJECT_ROOT/storage/app/"
ls -la "$PROJECT_ROOT/storage/app/public/" 2>/dev/null || echo "public 目錄不存在"
ls -la "$PROJECT_ROOT/storage/app/private/" 2>/dev/null || echo "private 目錄不存在"

echo ""
echo "=== 設置完成 ==="
echo "✓ 存儲目錄權限已設置"
echo "✓ PDF 合併目錄: storage/app/public/pdf_merges (公共訪問)"
echo "✓ 推薦函目錄: storage/app/private/recommendations (私有訪問)"
echo "✓ Storage link 已創建"

echo ""
echo "測試建議："
echo "1. 執行測試指令: php artisan pdf:test-compression --list-available"
echo "2. 檢查 Web 服務器是否可以訪問 storage/app/public 目錄"
echo "3. 確認防火牆設置允許外部訪問"
