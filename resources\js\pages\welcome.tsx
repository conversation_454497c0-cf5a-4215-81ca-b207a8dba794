import AppLogo from '@/components/app-logo';
import { LanguageSwitcher } from '@/components/language-switcher';
import { useLanguage } from '@/hooks/use-language';
import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';

// 推薦函系統首頁 - 現代化設計，專為推薦函系統定制
export default function Welcome() {
    const { t } = useLanguage();

    return (
        <>
            <Head title={t('common.appName')}>
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />
            </Head>

            {/* 現代化首頁設計 */}
            <div className="from-green-58 via-whit7 to-green-80 flex min-h-screen flex-col bg-gradient-to-b">
                {/* 導航欄 */}
                <header className="relative z-10 border-b border-gray-200/50 bg-white/80 backdrop-blur-sm">
                    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                        <div className="flex h-16 items-center justify-between">
                            {/* Logo 區域 */}
                            <div className="flex items-center space-x-3">
                                <AppLogo />
                            </div>

                            {/* 右側導航 */}
                            <div className="flex items-center space-x-4">
                                <LanguageSwitcher />
                            </div>
                        </div>
                    </div>
                </header>

                {/* 主要內容區域 */}
                <main className="flex-1">
                    {/* Hero 區域 */}
                    <div className="relative min-h-[calc(100vh-138px)] overflow-hidden border-b border-gray-100 bg-gradient-to-r from-blue-50 to-green-50">
                        <div className="mx-auto max-w-7xl px-4 py-24 sm:px-6 lg:px-8">
                            <div className="text-center">
                                <h1 className="mb-6 text-4xl font-bold text-gray-900 md:text-6xl">
                                    <span className="bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">
                                        {t('common.appName')}
                                    </span>
                                </h1>
                                <p className="mx-auto mb-8 max-w-3xl text-xl leading-relaxed text-gray-600">{t('welcome.welcomeDescription')}</p>

                                {/* 入口卡片 */}
                                <div className="mx-auto grid max-w-4xl gap-8 md:grid-cols-2">
                                    {/* 申請人入口 */}
                                    <div className="transform rounded-2xl border border-gray-100 bg-white p-8 shadow-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl">
                                        <div className="text-center">
                                            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-green-500 to-green-600">
                                                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                                    />
                                                </svg>
                                            </div>
                                            <h3 className="mb-4 text-2xl font-bold text-gray-900">考生</h3>
                                            <p className="mb-6 leading-relaxed text-gray-600">請透過報名系統登入，管理您的推薦人</p>
                                            <a
                                                href="http://localhost:18002/"
                                                target="_blank"
                                                className="inline-flex items-center rounded-lg bg-green-600 px-4 py-2 text-sm font-medium text-white transition-colors duration-200 hover:bg-green-700"
                                            >
                                                前往報名系統
                                            </a>
                                        </div>
                                    </div>

                                    {/* 推薦人入口 */}
                                    <div className="transform rounded-2xl border border-gray-100 bg-white p-8 shadow-xl transition-all duration-300 hover:-translate-y-1 hover:shadow-2xl">
                                        <div className="text-center">
                                            <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-green-700 to-green-700">
                                                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                    />
                                                </svg>
                                            </div>
                                            <h3 className="mb-4 text-2xl font-bold text-gray-900">推薦人</h3>
                                            <p className="mb-6 leading-relaxed text-gray-600">請使用邀請信中的連結登入，填寫推薦函</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>

                {/* 頁尾 */}
                <footer className="border-t border-gray-200 bg-gray-50">
                    <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <p className="text-gray-600">© 2025 National United University. All rights reserved。</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}
