<?php

/**
 * 驗證 PDF 壓縮系統變更的腳本
 */

echo "=== PDF 壓縮系統變更驗證 ===\n\n";

// 檢查必要的檔案是否存在
$requiredFiles = [
    'app/Jobs/ProcessPdfMergeJob.php',
    'app/Services/FilePackagingService.php',
    'app/Console/Commands/TestPdfCompressionCommand.php',
    'app/Http/Controllers/Api/PdfMergeApiController.php',
    'docs/PDF_COMPRESSION_API_MANUAL.md',
    'docs/PDF_COMPRESSION_CHANGES_SUMMARY.md',
];

echo "檢查必要檔案...\n";
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "✓ $file\n";
    } else {
        echo "✗ $file (缺少)\n";
    }
}

echo "\n";

// 檢查關鍵變更
echo "檢查關鍵變更...\n";

// 1. 檢查 FilePackagingService 的 ZIP 檔案命名
$packagingService = file_get_contents('app/Services/FilePackagingService.php');
if (strpos($packagingService, 'recommendations_{$examId}_{$examYear}') !== false) {
    echo "✓ ZIP 檔案命名格式已更新\n";
} else {
    echo "✗ ZIP 檔案命名格式未更新\n";
}

// 2. 檢查 ProcessPdfMergeJob 的目錄結構
$mergeJob = file_get_contents('app/Jobs/ProcessPdfMergeJob.php');
if (strpos($mergeJob, 'zip_path" => "{$autono}/{$fileName}"') !== false) {
    echo "✓ 目錄結構已更新為 autono 分類\n";
} else {
    echo "✗ 目錄結構未正確更新\n";
}

// 3. 檢查索引命名
if (strpos($mergeJob, '$fileName = "{$fileIndex}.pdf"') !== false) {
    echo "✓ 檔案索引命名已實現\n";
} else {
    echo "✗ 檔案索引命名未實現\n";
}

// 4. 檢查 README 內容更新
if (strpos($packagingService, '以索引命名: 1.pdf, 2.pdf, 3.pdf') !== false) {
    echo "✓ README 說明已更新\n";
} else {
    echo "✗ README 說明未更新\n";
}

// 5. 檢查操作手冊更新
$manual = file_get_contents('docs/PDF_COMPRESSION_API_MANUAL.md');
if (strpos($manual, 'recommendations_2_114_') !== false) {
    echo "✓ 操作手冊已更新\n";
} else {
    echo "✗ 操作手冊未更新\n";
}

// 6. 檢查公共下載路由
$controller = file_get_contents('app/Http/Controllers/Api/PdfMergeApiController.php');
if (strpos($controller, 'public function publicDownload') !== false) {
    echo "✓ 公共下載方法已添加\n";
} else {
    echo "✗ 公共下載方法未添加\n";
}

echo "\n";

// 檢查新增的腳本檔案
echo "檢查新增的腳本檔案...\n";
$scriptFiles = [
    'scripts/setup-storage-permissions.sh',
    'scripts/setup-storage-permissions.bat',
    'scripts/test-pdf-compression.sh',
    'scripts/test-new-structure.sh',
    'scripts/fix-pdf-compression-issues.sh',
];

foreach ($scriptFiles as $file) {
    if (file_exists($file)) {
        echo "✓ $file\n";
        // 檢查執行權限 (Unix 系統)
        if (PHP_OS_FAMILY !== 'Windows' && is_executable($file)) {
            echo "  ✓ 具有執行權限\n";
        } elseif (PHP_OS_FAMILY !== 'Windows') {
            echo "  ⚠️  缺少執行權限，請執行: chmod +x $file\n";
        }
    } else {
        echo "✗ $file (缺少)\n";
    }
}

echo "\n";

// 檢查存儲目錄
echo "檢查存儲目錄...\n";
$storageDirectories = [
    'storage/app/public/pdf_merges',
    'storage/app/private/recommendations',
];

foreach ($storageDirectories as $dir) {
    if (is_dir($dir)) {
        echo "✓ $dir\n";
        if (is_writable($dir)) {
            echo "  ✓ 可寫入\n";
        } else {
            echo "  ✗ 不可寫入\n";
        }
    } else {
        echo "✗ $dir (不存在)\n";
        echo "  建議執行: mkdir -p $dir\n";
    }
}

// 檢查 storage link
if (is_link('public/storage')) {
    echo "✓ Storage link 存在\n";
} else {
    echo "✗ Storage link 不存在\n";
    echo "  建議執行: php artisan storage:link\n";
}

echo "\n";

// 總結
echo "=== 驗證總結 ===\n";
echo "主要變更:\n";
echo "✓ ZIP 檔案命名: recommendations_{exam_id}_{exam_year}_{timestamp}.zip\n";
echo "✓ 目錄結構: 直接依照考生 autono 分類資料夾\n";
echo "✓ 檔案命名: 推薦函以索引命名 (1.pdf, 2.pdf, 3.pdf...)\n";
echo "✓ 公共下載: 無需認證的下載端點\n";
echo "✓ 測試工具: 完整的測試和驗證腳本\n";

echo "\n建議的下一步:\n";
echo "1. 執行權限設置: ./scripts/setup-storage-permissions.sh\n";
echo "2. 執行功能測試: php artisan pdf:test-compression --list-available\n";
echo "3. 執行結構測試: ./scripts/test-new-structure.sh\n";
echo "4. 檢查 Web 服務器配置確保可以訪問 storage/app/public\n";

echo "\n=== 驗證完成 ===\n";
