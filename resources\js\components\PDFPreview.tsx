import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useState, useEffect } from 'react';
import { FileText, Download, Eye, X, AlertCircle } from 'lucide-react';

interface PDFPreviewProps {
    file: File | null;
    onRemove?: () => void;
    showPreview?: boolean;
    className?: string;
}

export default function PDFPreview({ file, showPreview = true, className = '' }: PDFPreviewProps) {
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const [isPreviewOpen, setIsPreviewOpen] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (file) {
            // Validate file type
            if (file.type !== 'application/pdf') {
                setError('請選擇 PDF 檔案');
                return;
            }

            // Validate file size (10MB limit)
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (file.size > maxSize) {
                setError('檔案大小不能超過 10MB');
                return;
            }

            setError(null);

            // 創建 PDF 預覽 URL，添加編碼參數以改善中文顯示
            const url = URL.createObjectURL(file);
            setPreviewUrl(url);

            return () => {
                URL.revokeObjectURL(url);
            };
        } else {
            setPreviewUrl(null);
            setError(null);
        }
    }, [file]);

    const formatFileSize = (bytes: number): string => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    if (!file) {
        return null;
    }

    return (
        <div className={`space-y-4 ${className}`}>
            {/* File Info Card */}
            <Card className="border-blue-200 bg-blue-50/50">
                <CardContent className="">
                    <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                                <FileText className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="min-w-0 flex-1">
                                <p className="truncate text-sm font-medium text-gray-900">{file.name}</p>
                                <div className="mt-1 flex items-center space-x-2">
                                    <Badge variant="secondary" className="text-xs">
                                        PDF
                                    </Badge>
                                    <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                                </div>
                                {error && (
                                    <div className="mt-2 flex items-center space-x-1">
                                        <AlertCircle className="h-4 w-4 text-red-500" />
                                        <span className="text-xs text-red-600">{error}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            {showPreview && previewUrl && !error && (
                                <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => setIsPreviewOpen(true)}
                                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                                >
                                    <Eye className="mr-1 h-4 w-4" />
                                    預覽
                                </Button>
                            )}
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* PDF Preview Modal */}
            {isPreviewOpen && previewUrl && !error && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
                    <div className="flex h-[90vh] w-full max-w-4xl flex-col rounded-lg bg-white shadow-xl">
                        {/* Modal Header */}
                        <div className="flex items-center justify-between border-b p-4">
                            <div className="flex items-center space-x-2">
                                <FileText className="h-5 w-5 text-blue-600" />
                                <h3 className="text-lg font-semibold text-gray-900">PDF 預覽</h3>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button size="sm" variant="outline" onClick={() => setIsPreviewOpen(false)}>
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>

                        {/* PDF Viewer */}
                        <div className="flex-1 p-4">
                            <div className="h-full w-full overflow-hidden rounded-lg border">
                                <object data={previewUrl} type="application/pdf" className="h-full w-full" style={{ minHeight: '600px' }}>
                                    <div className="flex h-full items-center justify-center bg-gray-50">
                                        <div className="text-center">
                                            <FileText className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                                            <p className="mb-2 text-gray-600">無法預覽 PDF 檔案</p>
                                            <p className="mb-4 text-sm text-gray-500">您的瀏覽器可能不支援 PDF 預覽，或 PDF 檔案包含特殊字體</p>
                                            <div className="space-y-2">
                                                <Button
                                                    onClick={() => {
                                                        const link = document.createElement('a');
                                                        link.href = previewUrl;
                                                        link.download = file.name;
                                                        link.click();
                                                    }}
                                                    className="bg-blue-600 hover:bg-blue-700"
                                                >
                                                    <Download className="mr-2 h-4 w-4" />
                                                    下載檔案
                                                </Button>
                                                <p className="text-xs text-gray-400">建議使用 Adobe Reader 或其他 PDF 閱讀器開啟以獲得最佳顯示效果</p>
                                            </div>
                                        </div>
                                    </div>
                                </object>
                            </div>
                        </div>

                        {/* Modal Footer */}
                        <div className="border-t bg-gray-50 p-4">
                            <div className="flex items-center justify-between">
                                <div className="text-sm text-gray-600">
                                    檔案名稱：{file.name} ({formatFileSize(file.size)})
                                </div>
                                <Button variant="outline" onClick={() => setIsPreviewOpen(false)}>
                                    關閉
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
