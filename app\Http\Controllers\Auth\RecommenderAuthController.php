<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Recommender;
use App\Services\AuthenticationService;
use App\Models\LoginLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;

/**
 * 推薦人認證控制器
 *
 * 使用統一的 AuthenticationService 進行認證處理
 */
class RecommenderAuthController extends Controller
{
    protected AuthenticationService $authService;

    public function __construct(AuthenticationService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * 透過 Token 進行跳轉登入
     *
     * 處理推薦人透過信件中的連結登入
     * 使用統一的 AuthenticationService 進行認證處理
     */
    public function authenticateWithToken(string $token, Request $request)
    {
        try {
            // 1. 驗證 Token 並查找推薦人
            $recommender = Recommender::where('login_token', $token)
                ->where(function ($query) {
                    $query->whereNull('token_expires_at')
                        ->orWhere('token_expires_at', '>', now());
                })
                ->first();

            if (!$recommender) {
                Log::warning('推薦人 Token 登入失敗：Token 無效', ['token' => $token]);
                return $this->showAuthFailure('Token 無效或已過期，請聯繫申請人重新發送邀請');
            }

            // 2. 使用統一認證服務處理推薦人 Token 登入
            $result = $this->authService->loginRecommenderByToken(
                $token,
                $request->ip()
            );

            if (!$result['success']) {
                return $this->showAuthFailure($result['message']);
            }

            // 3. 記錄登入成功
            LoginLog::logSuccess(Auth::id());

            // 4. 檢查使用者協議
            $user = Auth::user();
            if (!\App\Models\UserAgreement::hasAgreed($user)) {
                return redirect()->route('user-agreement.show');
            }

            // 5. 跳轉到儀表板
            return redirect()->route('dashboard')->with('success', '歡迎使用推薦函系統！');
        } catch (\Exception $e) {
            Log::error('推薦人 Token 登入控制器異常', [
                'token' => $token,
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return $this->showAuthFailure('登入失敗，請稍後再試或聯繫系統管理員');
        }
    }

    /**
     * 顯示認證失敗頁面
     */
    private function showAuthFailure(string $message)
    {
        return inertia('auth/auth-failure', [
            'message' => $message,
            'type' => 'recommender'
        ]);
    }

    /**
     * 處理推薦人登出
     * 使用統一認證服務處理登出邏輯
     */
    public function logout()
    {
        $this->authService->logout('recommender');
        return redirect()->route('home')->with('success', '您已成功登出。');
    }

    /**
     * 更新推薦人個人資料
     */
    public function updateProfile(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return back()->withErrors(['error' => '請先登入']);
            }

            // 驗證輸入資料
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'title' => 'nullable|string|max:255',
                'department' => 'nullable|string|max:255',
                'phone' => 'nullable|string|max:20',
            ]);

            // 更新用戶基本資料
            DB::table('users')
                ->where('id', $user->id)
                ->update([
                    'updated_at' => now(),
                ]);

            // 查找並更新推薦人資料
            $recommender = \App\Models\Recommender::where('user_id', $user->id)->first();
            if ($recommender) {
                $recommender->update([
                    'name' => $validated['name'],
                    'title' => $validated['title'],
                    'department' => $validated['department'],
                    'phone' => $validated['phone'],
                ]);
            }

            return back()->with('success', '個人資料更新成功');
        } catch (\Exception $e) {
            Log::error('推薦人個人資料更新失敗', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return back()->withErrors(['error' => '個人資料更新失敗：' . $e->getMessage()]);
        }
    }
}
