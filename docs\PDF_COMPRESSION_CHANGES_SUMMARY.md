# PDF 壓縮系統變更總結

## 變更概述

本次更新將原本的 PDF 合併系統調整為 PDF 壓縮系統，以符合外部系統的需求。主要變更包括：

1. **功能調整**：從 PDF 合併改為直接壓縮
2. **檔案命名**：移除時間戳記，使用索引值格式
3. **覆蓋機制**：支持相同推薦人重複上傳時覆蓋
4. **測試工具**：新增測試指令
5. **文檔完善**：提供詳細操作手冊

## 檔案變更清單

### 核心邏輯變更

#### 1. `app/Jobs/ProcessPdfMergeJob.php`

- **變更內容**：將 PDF 合併邏輯改為直接壓縮
- **主要修改**：
    - 新增 `prepareApplicantFiles()` 方法
    - 修改 `handle()` 方法邏輯
    - 使用 `createDirectZipPackage()` 替代原本的合併流程

#### 2. `app/Services/FilePackagingService.php`

- **變更內容**：新增直接壓縮功能
- **主要修改**：
    - 新增 `createDirectZipPackage()` 方法
    - 新增 `addDirectFileToZip()` 方法
    - 新增 `addDirectReadmeToZip()` 方法
    - 新增 `ensureZipDirectoryExists()` 方法
    - 新增 `generateDirectReadmeContent()` 方法

#### 3. `app/Services/FileStorageService.php`

- **變更內容**：調整檔案命名規則
- **主要修改**：
    - 修改 `generateFileName()` 方法，移除時間戳記
    - 更新 `storePdfFile()` 方法，支持覆蓋機制
    - 修復 `downloadFile()` 方法

### 新增檔案

#### 4. `app/Console/Commands/TestPdfCompressionCommand.php`

- **功能**：提供快速測試 PDF 壓縮功能的指令
- **使用方式**：
    ```bash
    php artisan pdf:test-compression --list-available
    php artisan pdf:test-compression --exam_id=2 --exam_year=114 --dry-run
    php artisan pdf:test-compression --exam_id=2 --exam_year=114
    ```

#### 5. `docs/PDF_COMPRESSION_API_MANUAL.md`

- **功能**：詳細的 API 操作手冊
- **內容包括**：
    - API 端點說明
    - CSV 參照檔格式
    - ZIP 檔案結構
    - 錯誤處理
    - 實作範例
    - 部署注意事項

#### 6. `tests/Feature/PdfCompressionApiTest.php`

- **功能**：API 功能測試
- **測試範圍**：
    - 啟動壓縮任務
    - 查詢任務狀態
    - 處理壓縮作業
    - 下載完成檔案
    - 錯誤處理

## 功能變更詳細說明

### 1. PDF 處理邏輯變更

**原本邏輯**：

1. 獲取推薦函 → 2. 按考生分組 → 3. 合併每位考生的 PDF → 4. 打包成 ZIP

**新邏輯**：

1. 獲取推薦函 → 2. 按考生分組 → 3. 準備檔案列表 → 4. 直接打包成 ZIP

### 2. 檔案命名變更

**原本格式**：`{推薦人ID}_{時間戳記}.pdf`
**新格式**：`{推薦人ID}.pdf`

**優點**：

- 支持覆蓋機制
- 檔案名稱更簡潔
- 符合外部系統需求

### 3. ZIP 檔案結構

```
recommendations_2_114_2024-01-15_10-30-00.zip
├── 348/                         # 考生流水號目錄
│   ├── 1.pdf                    # 第一封推薦函
│   ├── 2.pdf                    # 第二封推薦函
│   └── 3.pdf                    # 第三封推薦函
├── 349/                         # 另一位考生
│   ├── 1.pdf
│   └── 2.pdf
└── 350/
    └── 1.pdf
```

### 4. 檔案命名規則

- **ZIP 檔案名**: `recommendations_{exam_id}_{exam_year}_{timestamp}.zip`
- **目錄結構**: 直接依照考生 autono 分類資料夾
- **推薦函檔案**: 以索引命名 (1.pdf, 2.pdf, 3.pdf...)

### 5. CSV 參照檔格式

```csv
"exam_id","stu_year(exam_year)","autono","stu_no","stu_name","dep_no","new_file_path"
"2","114","348","12010001","VＸ224001","111","348/1.pdf"
"2","114","348","12010001","VＸ224001","111","348/2.pdf"
"2","114","349","12010002","VＸ224002","111","349/1.pdf"
```

## API 使用方式

### 1. 啟動壓縮任務

```bash
curl -X POST https://rec-letter.test/api/pdf-merge/start-merge \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"exam_id": "2", "exam_year": 114}'
```

### 2. 查詢任務狀態

```bash
curl -X GET "https://rec-letter.test/api/pdf-merge/merge-status?task_id=task_123" \
  -H "X-API-Key: your-api-key"
```

### 3. 下載檔案

```bash
curl -X GET "https://rec-letter.test/api/pdf-merge/download?task_id=task_123" \
  -H "X-API-Key: your-api-key" \
  -o compressed_files.zip
```

## 測試指令使用

### 列出可用選項

```bash
php artisan pdf:test-compression --list-available
```

### 乾跑測試

```bash
php artisan pdf:test-compression --exam_id=2 --exam_year=114 --dry-run
```

### 執行測試

```bash
php artisan pdf:test-compression --exam_id=2 --exam_year=114
```

## 部署檢查清單

### 1. 環境設定

- [ ] 確認 API 密鑰已更新
- [ ] 確認 IP 白名單已設定
- [ ] 確認存儲路徑權限正確

### 2. 功能測試

- [ ] 執行測試指令驗證功能
- [ ] 測試 API 端點回應
- [ ] 驗證 ZIP 檔案結構

### 3. 監控設定

- [ ] 設定日誌監控
- [ ] 設定磁碟空間監控
- [ ] 設定任務執行監控

## 向後相容性

### 保持相容的部分

- API 端點路徑不變
- 請求參數格式不變
- 回應格式不變
- 資料庫結構不變

### 變更的部分

- ZIP 檔案內部結構
- 檔案命名規則
- 處理邏輯（從合併改為壓縮）

## 注意事項

1. **檔案覆蓋**：相同推薦人重複上傳會覆蓋舊檔案
2. **目錄結構**：ZIP 檔案內按 exam_id/exam_year 組織
3. **檔案命名**：使用 autono 作為主要識別
4. **測試環境**：建議先在測試環境驗證所有功能

## 聯絡資訊

如有問題或需要支援，請聯繫：

- 系統管理員：[聯絡方式]
- 技術支援：[聯絡方式]

---

**變更日期**：2024-01-15
**版本**：2.0
**狀態**：已完成
