<?php

namespace App\Services;

use App\Models\User;
use App\Models\Recommender;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class RecommenderService
{
    /**
     * Create or get a recommender user and profile.
     *
     * @param array $recommenderData
     * @param array|null $examInfo 考試資訊 (exam_id, exam_year)
     * @return array ['user' => User, 'recommender' => Recommender, 'created' => bool]
     */
    public function createOrGetRecommender(array $recommenderData, ?array $examInfo = null): array
    {
        $email = $recommenderData['email'];
        $name = $recommenderData['name'] ?? '';
        $title = $recommenderData['title'] ?? '';
        $department = $recommenderData['department'] ?? '';
        $phone = $recommenderData['phone'] ?? '';

        // 考試資訊
        $exam_year = $examInfo['exam_year'] ?? null;
        $exam_id = $examInfo['exam_id'] ?? null;

        return DB::transaction(function () use ($email, $name, $title, $department, $phone, $exam_year, $exam_id) {
            $userCreated = false;
            $recommenderCreated = false;

            // Step 1: 建立或取得"使用者"帳號
            $user = User::where('email', $email)->first();

            if (!$user) {
                $user = User::create([
                    'name' => $name,
                    'email' => $email,
                    'role' => 'recommender',
                    'password' => null, // Recommenders use token login
                    'email_verified_at' => now(),
                ]);
                $userCreated = true;

                Log::info('建立新的推薦人使用者帳號', [
                    'user_id' => $user->id,
                    'email' => $email,
                    'name' => $name
                ]);
            } else {
                // Update user role if needed
                if ($user->role !== 'recommender') {
                    $user->update(['role' => 'recommender']);
                    Log::info('Updated user role to recommender', [
                        'user_id' => $user->id,
                        'email' => $email
                    ]);
                }
            }

            // Step 2: 建立或取得"推薦人"資料
            $recommender = Recommender::where('user_id', $user->id)->first();

            // 如果推薦人不存在，則建立新的推薦人資料 （初次建立時，依照新增來源設定個人資訊，後續推薦人可自行維護）
            if (!$recommender) {
                Log::info('嘗試新增推薦人資料', [
                    'user_id' => $user->id,
                    'email' => $email,
                    'name' => $name ?: $user->name,
                    'title' => $title,
                    'department' => $department,
                    'phone' => $phone,
                    'login_token' => '尚未生成',
                    'exam_year' => $exam_year,
                    'exam_id' => $exam_id,
                ]);

                $recommender = Recommender::create([
                    'user_id' => $user->id,
                    'email' => $email,
                    'name' => $name ?: $user->name,
                    'title' => $title,
                    'department' => $department,
                    'phone' => $phone,
                    'login_token' => $this->generateUniqueLoginToken(),
                    'exam_year' => $exam_year,
                    'exam_id' => $exam_id,
                ]);
                $recommenderCreated = true;

                Log::info('建立新的推薦人資料', [
                    'recommender_id' => $recommender->id,
                    'user_id' => $user->id,
                    'email' => $email,
                    'exam_year' => $exam_year,
                    'exam_id' => $exam_id,
                ]);
            } else {
                // 更新推薦人的考試資訊
                if ($exam_year || $exam_id) {
                    $updateData = [];
                    if ($exam_year) $updateData['exam_year'] = $exam_year;
                    if ($exam_id) $updateData['exam_id'] = $exam_id;

                    if (!empty($updateData)) {
                        $recommender->update($updateData);
                        Log::info('更新推薦人考試資訊', [
                            'recommender_id' => $recommender->id,
                            'updated_fields' => array_keys($updateData),
                        ]);
                    }
                }
            }

            return [
                'user' => $user,
                'recommender' => $recommender,
                'user_created' => $userCreated,
                'recommender_created' => $recommenderCreated,
                'created' => $userCreated || $recommenderCreated,
            ];
        });
    }

    /**
     * 產生唯一且安全的登入 Token
     *
     * 使用更複雜的 Token 生成策略：
     * - 結合時間戳和隨機字串
     * - 使用 SHA256 雜湊增加安全性
     * - 確保 Token 唯一性
     */
    private function generateUniqueLoginToken(): string
    {
        do {
            // 結合時間戳、隨機字串和推薦人 ID 來增加複雜度
            $timestamp = now()->timestamp;
            $randomString = Str::random(32);
            $salt = config('app.key', 'default-salt');

            // 使用 SHA256 雜湊生成更安全的 Token
            $rawToken = $timestamp . $randomString . $salt;
            $token = hash('sha256', $rawToken);

            // 截取前 64 個字符作為最終 Token
            $token = substr($token, 0, 64);
        } while (Recommender::where('login_token', $token)->exists());

        Log::debug('Generated secure login token', [
            'token_length' => strlen($token),
            'timestamp' => $timestamp
        ]);

        return $token;
    }

    /**
     * Get recommender by email.
     */
    public function getRecommenderByEmail(string $email): ?Recommender
    {
        return Recommender::where('email', $email)->first();
    }

    /**
     * Get recommender by login token.
     */
    public function getRecommenderByToken(string $token): ?Recommender
    {
        return Recommender::where('login_token', $token)->first();
    }

    /**
     * Update recommender's last login time.
     */
    public function updateLastLogin(Recommender $recommender): void
    {
        $recommender->update(['last_login_at' => now()]);
    }

    /**
     * Generate new login token for recommender.
     */
    public function regenerateLoginToken(Recommender $recommender): string
    {
        $newToken = $this->generateUniqueLoginToken();
        $recommender->update(['login_token' => $newToken]);

        Log::info('Regenerated login token for recommender', [
            'recommender_id' => $recommender->id,
            'email' => $recommender->email
        ]);

        return $newToken;
    }

    /**
     * Get recommender's login URL.
     */
    public function getLoginUrl(Recommender $recommender): string
    {
        return route('recommender.auth', ['token' => $recommender->login_token]);
    }
}
