import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { router } from '@inertiajs/react';
import { BarChart3, FileText, Settings, Activity, Mail, ScanFace, ArrowRight, Users, CheckCircle, Clock, AlertTriangle } from 'lucide-react';

interface AdminRecommendation {
    id: number;
    status: string;
    created_at?: string;
}

interface OverviewTabProps {
    recommendations: AdminRecommendation[];
}

export default function OverviewTab({ recommendations }: OverviewTabProps) {
    // 統計資料
    const totalRecommendations = recommendations.length;
    const submittedCount = recommendations.filter((rec) => rec.status === 'submitted').length;
    const pendingCount = recommendations.filter((rec) => rec.status === 'pending').length;
    const declinedCount = recommendations.filter((rec) => rec.status === 'declined').length;

    // 最近7天的推薦函
    const recentRecommendations = recommendations.filter((rec) => {
        if (!rec.created_at) return false;
        const createdDate = new Date(rec.created_at);
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return createdDate >= sevenDaysAgo;
    }).length;

    return (
        <div className="space-y-6 p-6">
            {/* 頁面標題 */}
            <div>
                <h1 className="text-3xl font-bold tracking-tight">管理員儀表板</h1>
                <p className="text-muted-foreground">系統概覽</p>
            </div>

            {/* todo 統計卡片 (推薦函資料(元件化)) */}
        </div>
    );
}
