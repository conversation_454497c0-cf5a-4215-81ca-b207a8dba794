import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/use-language';
import { AlertTriangle, X } from 'lucide-react';
import React from 'react';

interface ConfirmationDialogProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    message: string;
    subMessage?: string;
    confirmText?: string;
    cancelText?: string;
    type?: 'warning' | 'danger' | 'info';
    isLoading?: boolean;
}

export default function ConfirmationDialog({
    isOpen,
    onClose,
    onConfirm,
    title,
    message,
    subMessage,
    confirmText,
    cancelText,
    type = 'warning',
    isLoading = false,
}: ConfirmationDialogProps) {
    const { t } = useLanguage();

    if (!isOpen) return null;

    const getTypeStyles = () => {
        switch (type) {
            case 'danger':
                return {
                    iconColor: 'text-red-600',
                    bgColor: 'bg-red-50',
                    borderColor: 'border-red-200',
                    confirmButtonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
                };
            case 'warning':
                return {
                    iconColor: 'text-yellow-600',
                    bgColor: 'bg-yellow-50',
                    borderColor: 'border-yellow-200',
                    confirmButtonClass: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
                };
            case 'info':
            default:
                return {
                    iconColor: 'text-blue-600',
                    bgColor: 'bg-blue-50',
                    borderColor: 'border-blue-200',
                    confirmButtonClass: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
                };
        }
    };

    const styles = getTypeStyles();

    return (
        <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4"
            role="dialog"
            aria-modal="true"
            aria-labelledby="dialog-title"
            aria-describedby="dialog-description"
        >
            <div className="w-full max-w-md scale-100 transform rounded-lg bg-white p-6 shadow-xl transition-transform">
                {/* Header */}
                <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                        <div className={`flex-shrink-0 rounded-full p-2 ${styles.bgColor} ${styles.borderColor} border`}>
                            <AlertTriangle className={`h-5 w-5 ${styles.iconColor}`} />
                        </div>
                        <div className="flex-1">
                            <h3 id="dialog-title" className="text-lg font-semibold text-gray-900">
                                {title}
                            </h3>
                            <p id="dialog-description" className="mt-2 text-sm text-gray-600">
                                {message}
                            </p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="flex-shrink-0 rounded-md p-1 text-gray-400 hover:text-gray-600 focus:ring-2 focus:ring-gray-500 focus:outline-none"
                        disabled={isLoading}
                    >
                        <X className="h-5 w-5" />
                    </button>
                </div>

                {/* Actions */}
                <div className="mt-6 flex flex-col-reverse gap-3 sm:flex-row sm:items-center sm:justify-end">
                    {subMessage && <p className="w-full text-center text-sm text-gray-500 sm:w-auto sm:text-right">{subMessage}</p>}

                    <Button variant="outline" onClick={onClose} disabled={isLoading} className="w-full sm:w-auto">
                        {cancelText || t('common.cancel')}
                    </Button>

                    <Button onClick={onConfirm} disabled={isLoading} className={`w-full text-white sm:w-auto ${styles.confirmButtonClass}`}>
                        {isLoading ? (
                            <div className="flex items-center space-x-2">
                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                                <span>{t('common.loading')}</span>
                            </div>
                        ) : (
                            confirmText || t('common.confirm')
                        )}
                    </Button>
                </div>
            </div>
        </div>
    );
}
