import ConfirmationDialog from '@/components/ConfirmationDialog';
import DynamicQuestionnaire from '@/components/DynamicQuestionnaire';
import { NoRecommendationsEmptyState } from '@/components/EmptyState';
import PDFPreview from '@/components/PDFPreview';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useLanguage } from '@/hooks/use-language';
import { SharedData } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { AlertCircle, Calendar, Clock, User, CheckCircle, XCircle, Mail } from 'lucide-react';
import { useState, useMemo, useEffect } from 'react';

interface RecommendationForRecommender {
    id: number;
    applicant_id: number;
    external_autono: string;
    department_name: string;
    program_type: string;
    status: string;
    submitted_at?: string;
    created_at?: string;
    pdf_path?: string;
    questionnaire_data?: any;
    applicant?: {
        user?: {
            name: string;
            email: string;
        };
    };
}

export default function DashboardRecommender() {
    const { recommendations, recommender, questionnaire_templates, submission_settings } = usePage<SharedData>().props;
    const { t } = useLanguage();

    // Existing states
    const [selectedRecommendation, setSelectedRecommendation] = useState<number | null>(null);
    const [submissionType, setSubmissionType] = useState<'pdf' | 'questionnaire' | null>(null);
    const [pdfFile, setPdfFile] = useState<File | null>(null);
    const [questionnaireData, setQuestionnaireData] = useState<{ [key: string]: string }>({});
    const [questionnaireTemplate, setQuestionnaireTemplate] = useState<any>(null);
    const [isQuestionnaireSubmitting, setIsQuestionnaireSubmitting] = useState(false);
    const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);

    // New states for enhanced functionality
    const [showProfileEdit, setShowProfileEdit] = useState(false);

    // 自動設定提交方式（如果只有一種可用）
    useEffect(() => {
        if (submission_settings) {
            if (submission_settings.allow_pdf_upload && !submission_settings.allow_questionnaire_submission) {
                setSubmissionType('pdf');
            } else if (!submission_settings.allow_pdf_upload && submission_settings.allow_questionnaire_submission) {
                setSubmissionType('questionnaire');
            }
        }
    }, [submission_settings]);

    useEffect(() => {
        // 當提交方式發生變化時
        if (submissionType === 'questionnaire') {
            // 如果選擇了問卷提交，則清除PDF文件
            setPdfFile(null);
        }
        if (submissionType === 'pdf') {
            // 如果選擇了PDF提交，則清除問卷資料
            setQuestionnaireData({});
        }
    }, [submissionType]);

    const [profileData, setProfileData] = useState({
        name: recommender?.name || '',
        title: recommender?.title || '',
        department: recommender?.department || '',
        phone: recommender?.phone || '',
        email: recommender?.email || '',
    });
    const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);
    const [profileUpdateStatus, setProfileUpdateStatus] = useState<{
        type: 'success' | 'error' | null;
        message: string;
    }>({ type: null, message: '' });

    // Confirmation dialog state
    const [confirmDialog, setConfirmDialog] = useState<{
        isOpen: boolean;
        type: 'decline' | 'withdraw' | 'submit' | null; // 只有婉拒或撤回
        recommendation: RecommendationForRecommender | null;
        isLoading: boolean;
        submissionType?: 'pdf' | 'questionnaire';
    }>({
        isOpen: false,
        type: null,
        recommendation: null,
        isLoading: false,
    });

    // Statistics
    const stats = useMemo(() => {
        if (!recommendations) return { pending: 0, completed: 0, total: 0 };

        // 待處理項目：pending
        const pending = recommendations.filter((rec: RecommendationForRecommender) => rec.status === 'pending').length;
        const completed = recommendations.filter((rec: RecommendationForRecommender) => rec.status === 'submitted').length;

        return {
            pending,
            completed,
            total: recommendations.length,
        };
    }, [recommendations]);
    const openConfirmDialog = (type: 'decline', recommendation: RecommendationForRecommender) => {
        setConfirmDialog({
            isOpen: true,
            type,
            recommendation,
            isLoading: false,
        });
    };

    // 預覽
    const previewPdf = (pdfPath: string) => {
        const url = `/files/preview-pdf?path=${encodeURIComponent(pdfPath)}`;
        window.open(url, '_blank');
    };

    // Close confirmation dialog
    const closeConfirmDialog = () => {
        setConfirmDialog({
            isOpen: false,
            type: null,
            recommendation: null,
            isLoading: false,
        });
    };

    // 個人資料的驗證邏輯
    const validateProfileData = () => {
        const errors: string[] = [];

        if (!profileData.name.trim()) {
            errors.push('姓名為必填欄位');
        }

        if (profileData.phone && !/^[\d\-\+\(\)\s]+$/.test(profileData.phone)) {
            errors.push('請輸入有效的電話號碼格式');
        }

        return errors;
    };

    // 處理個人資料更新
    const handleProfileUpdate = () => {
        // 驗證表單資料
        const validationErrors = validateProfileData();
        if (validationErrors.length > 0) {
            setProfileUpdateStatus({
                type: 'error',
                message: validationErrors.join('、'),
            });
            return;
        }

        setIsUpdatingProfile(true);
        setProfileUpdateStatus({ type: null, message: '' });

        router.post('/recommender/profile/update', profileData, {
            onError: (errors) => {
                console.error('Profile update failed:', errors);
                setProfileUpdateStatus({
                    type: 'error',
                    message: typeof errors === 'object' && errors.error ? errors.error : '個人資料更新失敗，請稍後再試',
                });
                setIsUpdatingProfile(false);
            },
            onSuccess: () => {
                setShowProfileEdit(false);
                setProfileUpdateStatus({
                    type: 'success',
                    message: '個人資料更新成功！',
                });
                setIsUpdatingProfile(false);

                // 3秒後清除成功訊息
                setTimeout(() => {
                    setProfileUpdateStatus({ type: null, message: '' });
                }, 3000);
            },
            onFinish: () => {
                setIsUpdatingProfile(false);
            },
        });
    };

    // 取消編輯個人資料
    const handleProfileEditCancel = () => {
        setShowProfileEdit(false);
        setProfileUpdateStatus({ type: null, message: '' });
        // 重置為原始值
        setProfileData({
            name: recommender?.name || '',
            title: recommender?.title || '',
            department: recommender?.department || '',
            phone: recommender?.phone || '',
            email: recommender?.email || '',
        });
    };

    // 處理確認對話框中的操作
    const handleConfirmedAction = () => {
        if (!confirmDialog.recommendation || !confirmDialog.type) return;

        setConfirmDialog((prev) => ({ ...prev, isLoading: true }));

        const rec = confirmDialog.recommendation;

        if (confirmDialog.type === 'submit') {
            // 確認提交推薦函
            closeConfirmDialog();
            if (confirmDialog.submissionType === 'questionnaire') {
                // 問卷提交 - 使用暫存的問卷資料
                handleSubmit(rec.id, questionnaireData);
            } else {
                // PDF 提交
                handleSubmit(rec.id);
            }
            return;
        }

        if (confirmDialog.type === 'withdraw') {
            // 撤回推薦函
            router.post(
                `/recommendations/${rec.id}/withdraw`,
                {},
                {
                    preserveState: false,
                    onSuccess: () => {
                        closeConfirmDialog();
                        router.reload();
                    },
                    onError: (errors) => {
                        console.error('撤回推薦函失敗:', errors);
                        setConfirmDialog((prev) => ({ ...prev, isLoading: false }));
                    },
                },
            );
        } else {
            // 拒絕推薦函
            const endpoint = confirmDialog.type === 'decline' ? 'decline' : undefined;
            router.post(
                `/recommendations/${rec.id}/${endpoint}`,
                {},
                {
                    preserveState: false,
                    onSuccess: () => {
                        closeConfirmDialog();
                        router.reload();
                    },
                    onError: (errors) => {
                        console.error(`Error ${confirmDialog.type}ing recommendation:`, errors);
                        setConfirmDialog((prev) => ({ ...prev, isLoading: false }));
                    },
                },
            );
        }
    };

    // 暫不使用（是否允許推薦人取消or人工處理）
    const handleWithdrawRecommendation = (recommendation: RecommendationForRecommender) => {
        setConfirmDialog({
            isOpen: true,
            type: 'withdraw',
            recommendation,
            isLoading: false,
        });
    };

    const handleDecline = (rec: RecommendationForRecommender) => {
        openConfirmDialog('decline', rec);
    };

    // 打開提交確認對話框
    const handleSubmitConfirmation = (rec: RecommendationForRecommender, submissionType: 'pdf' | 'questionnaire') => {
        setConfirmDialog({
            isOpen: true,
            type: 'submit',
            recommendation: rec,
            isLoading: false,
            submissionType,
        });
    };

    const loadQuestionnaireTemplate = (recId: number) => {
        setIsLoadingTemplate(true);

        // 從預載入的資料中查找對應的問卷模板
        const recommendation = recommendations.find((r) => r.id === recId);
        if (recommendation && questionnaire_templates) {
            const templateKey = `${recommendation.department_name}_${recommendation.program_type}`;
            const template = (questionnaire_templates as Record<string, unknown>)[templateKey];

            if (template) {
                setQuestionnaireTemplate(template);
            } else {
                console.error('No questionnaire template found for this recommendation');
                setQuestionnaireTemplate(null);
            }
        } else {
            console.error('No questionnaire templates available');
            setQuestionnaireTemplate(null);
        }

        setIsLoadingTemplate(false);
    };

    // 自動判斷提交類型並處理提交
    const handleSubmit = (recId: number, customQuestionnaireData?: Record<string, unknown>) => {
        const formData = new FormData();

        // 自動判斷提交類型：如果有PDF文件則提交PDF，否則提交問卷
        if (pdfFile) {
            formData.append('pdf_file', pdfFile);
            console.log('提交PDF文件:', pdfFile.name);
        } else if (customQuestionnaireData || Object.keys(questionnaireData).length > 0) {
            const dataToSubmit = customQuestionnaireData || questionnaireData;
            formData.append('questionnaire_data', JSON.stringify(dataToSubmit));
            console.log('提交問卷資料:', dataToSubmit);
        } else {
            alert('請選擇上傳PDF文件或填寫問卷');
            return;
        }

        setIsQuestionnaireSubmitting(true);

        router.post(`/recommendations/${recId}/submit`, formData, {
            onSuccess: () => {
                setSelectedRecommendation(null);
                setPdfFile(null);
                setQuestionnaireData({});
                setQuestionnaireTemplate(null);
                setSubmissionType('pdf'); // 重置為預設值
                setIsQuestionnaireSubmitting(false);
                router.reload();
            },
            onError: (errors) => {
                console.error('Error submitting recommendation:', errors);
                const errorMessage = getErrorMessage(errors);
                alert(errorMessage);
                setIsQuestionnaireSubmitting(false);
            },
            onFinish: () => {
                setIsQuestionnaireSubmitting(false);
            },
        });
    };

    const handleQuestionnaireSubmit = (data: Record<string, unknown>) => {
        // 更新問卷資料狀態
        setQuestionnaireData(data as { [key: string]: string });

        // 顯示確認對話框
        const rec = recommendations.find((r) => r.id === selectedRecommendation);
        if (rec) {
            // 暫存問卷資料以便確認後提交
            setQuestionnaireData(data as { [key: string]: string });
            handleSubmitConfirmation(rec, 'questionnaire');
        }
    };

    // 簡單的錯誤處理函數
    const getErrorMessage = (errors: unknown): string => {
        if (typeof errors === 'string') return errors;
        if (errors && typeof errors === 'object') {
            const firstError = Object.values(errors)[0];
            if (Array.isArray(firstError)) {
                return firstError[0] as string;
            }
            return firstError as string;
        }
        return '發生未知錯誤，請稍後再試';
    };

    const getCardStyle = (status: string) => {
        switch (status) {
            case 'submitted':
                return 'border-green-200 bg-green-50/50 shadow-sm';
            case 'pending':
                return 'border-yellow-200 bg-yellow-50/50 shadow-sm';
            case 'withdrawn':
                return 'border-gray-200 bg-gray-50/50 shadow-sm';
            case 'declined':
                return 'border-gray-200 bg-gray-50/50 shadow-sm';
            default:
                return 'border-gray-200 bg-white shadow-sm';
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending':
                return (
                    <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                        待處理
                    </Badge>
                );
            case 'submitted':
                return (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                        已提交
                    </Badge>
                );
            case 'withdrawn':
                return (
                    <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                        考生已撤回
                    </Badge>
                );
            case 'declined':
                return (
                    <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                        已婉拒
                    </Badge>
                );
            default:
                return <Badge variant="secondary">未知</Badge>;
        }
    };

    return (
        <div className="space-y-6 p-6">
            <Head title="推薦函列表"></Head>

            {/* 重要提醒 */}
            {/* todo 提醒推薦人若有使用多個信箱，請務必自行確認其他帳號是否已完成(強調，UI、文字優化) */}
            <Card className={`border-2 ${stats.pending > 0 ? 'border-orange-300 bg-orange-50/80' : 'border-blue-200 bg-blue-50/50'}`}>
                <CardHeader>
                    <div className="flex items-center space-x-2">
                        <AlertCircle className={`h-5 w-5 ${stats.pending > 0 ? 'text-orange-600' : 'text-blue-600'}`} />
                        <CardTitle className={`text-lg ${stats.pending > 0 ? 'text-orange-900' : 'text-blue-900'}`}>
                            {t('dashboard.recommender.importantNotice')}
                        </CardTitle>
                    </div>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <Calendar className={`h-4 w-4 ${stats.pending > 0 ? 'text-orange-600' : 'text-blue-600'}`} />
                                <span className={`font-medium ${stats.pending > 0 ? 'text-orange-900' : 'text-blue-900'}`}>操作截止日期:</span>
                            </div>
                            <p className={`text-sm ${stats.pending > 0 ? 'text-orange-800' : 'text-blue-800'}`}>
                                {t('dashboard.recommender.deadlineDate')}
                            </p>
                            <p className={`text-xs ${stats.pending > 0 ? 'text-orange-700' : 'text-blue-700'}`}>
                                {t('dashboard.recommender.deadlineNote')}
                            </p>
                        </div>
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <User className={`h-4 w-4 ${stats.pending > 0 ? 'text-orange-600' : 'text-blue-600'}`} />
                                <span className={`font-medium ${stats.pending > 0 ? 'text-orange-900' : 'text-blue-900'}`}>
                                    {t('dashboard.recommender.pendingCount')}
                                </span>
                            </div>
                            <p className={`text-sm font-semibold ${stats.pending > 0 ? 'text-orange-800' : 'text-blue-800'}`}>
                                {stats.pending} {t('common.items')}
                            </p>
                            {stats.pending > 0 && (
                                <p className="animate-pulse text-xs font-medium text-orange-700">⚠️ 請優先處理待處理的推薦函邀請</p>
                            )}
                        </div>
                    </div>

                    {/* 多信箱提醒 */}
                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                        <div className="flex items-start gap-3">
                            {/* 文字區塊 */}
                            <div className="flex-1 space-y-2">
                                <p className="text-sm font-medium text-gray-900">
                                    若您使用多個電子信箱接收推薦函邀請，請務必確認各信箱中的推薦函是否皆已完成。
                                </p>
                                <p className="text-xs text-gray-700">建議：統一使用單一信箱接收推薦函邀請，以避免混淆與遺漏。</p>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* 編輯推薦人個人資料 todo RWD狀態下樣式調整 */}
            <Card className={`border-2 ${showProfileEdit ? 'border-orange-300' : ''}`}>
                <CardHeader className="">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                            <User className="h-5 w-5 text-gray-600" />
                            <CardTitle className="text-lg">
                                {t('dashboard.recommender.profileMaintenance')}
                                <span className="ml-2 text-sm text-blue-600">{profileData.email}</span>
                            </CardTitle>
                        </div>
                        <div className="flex justify-end space-x-2">
                            {showProfileEdit && (
                                <div className="flex items-center space-x-2">
                                    <span className="text-sm text-red-600">尚未保存</span>
                                    <Button onClick={handleProfileUpdate} disabled={isUpdatingProfile} size="sm">
                                        {isUpdatingProfile ? '更新中...' : t('common.save')}
                                    </Button>
                                </div>
                            )}
                            <Button variant="outline" size="sm" onClick={showProfileEdit ? handleProfileEditCancel : () => setShowProfileEdit(true)}>
                                {showProfileEdit ? '取消' : '編輯'}
                            </Button>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* 狀態提示 */}
                    {profileUpdateStatus.type && (
                        <Alert className={profileUpdateStatus.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                            {profileUpdateStatus.type === 'success' ? (
                                <CheckCircle className="h-4 w-4 text-green-600" />
                            ) : (
                                <XCircle className="h-4 w-4 text-red-600" />
                            )}
                            <AlertDescription className={`ml-2 ${profileUpdateStatus.type === 'success' ? 'text-green-800' : 'text-red-800'}`}>
                                {profileUpdateStatus.message}
                            </AlertDescription>
                        </Alert>
                    )}
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="name">{t('recommendations.form.recommenderName')}</Label>
                            <Input
                                id="name"
                                value={profileData.name}
                                disabled={!showProfileEdit}
                                className={!showProfileEdit ? 'bg-gray-100' : ''}
                                placeholder="請輸入姓名"
                                onChange={(e) => setProfileData((prev) => ({ ...prev, name: e.target.value }))}
                            />
                        </div>

                        <div>
                            <Label htmlFor="department">推薦人所屬單位</Label>
                            <Input
                                id="department"
                                value={profileData.department}
                                disabled={!showProfileEdit}
                                className={!showProfileEdit ? 'bg-gray-100' : ''}
                                placeholder="請輸入所屬單位"
                                onChange={(e) => setProfileData((prev) => ({ ...prev, department: e.target.value }))}
                            />
                        </div>
                        <div>
                            <Label htmlFor="title">{t('recommendations.form.recommenderTitle')}</Label>
                            <Input
                                id="title"
                                value={profileData.title}
                                disabled={!showProfileEdit}
                                className={!showProfileEdit ? 'bg-gray-100' : ''}
                                placeholder="請輸入職稱"
                                onChange={(e) => setProfileData((prev) => ({ ...prev, title: e.target.value }))}
                            />
                        </div>
                        <div>
                            <Label htmlFor="phone">{t('recommendations.form.recommenderPhone')}</Label>
                            <Input
                                id="phone"
                                value={profileData.phone}
                                disabled={!showProfileEdit}
                                className={!showProfileEdit ? 'bg-gray-100' : ''}
                                placeholder="請輸入聯絡電話"
                                onChange={(e) => setProfileData((prev) => ({ ...prev, phone: e.target.value }))}
                            />
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* 推薦函邀請列表 */}
            {!recommendations || recommendations.length === 0 ? (
                <NoRecommendationsEmptyState userRole="recommender" />
            ) : (
                <div className="space-y-4">
                    {recommendations.map((rec: RecommendationForRecommender) => (
                        <Card key={rec.id} className={getCardStyle(rec.status)}>
                            <CardContent className="py-0">
                                {/* 卡片主要內容區域 */}
                                <div className="space-y-4">
                                    {/* 考生基本資訊卡片 */}
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <h4 className="text-lg font-semibold text-gray-900">{rec.applicant?.user?.name || '考生姓名未提供'}</h4>
                                            <p className="text-sm text-gray-600">
                                                {rec.program_type} – {rec.department_name}
                                            </p>
                                        </div>
                                        <div className="ml-4">{getStatusBadge(rec.status)}</div>
                                    </div>

                                    {/* 卡片底部：時間訊息與操作按鈕 */}
                                    <div className="flex flex-col gap-3 border-t border-gray-200 pt-4 sm:flex-row sm:items-center sm:justify-between">
                                        {/* 左側：時間訊息 */}
                                        <div className="flex flex-col gap-1 text-sm text-gray-600">
                                            <div className="flex items-center gap-2">
                                                <Calendar className="h-4 w-4" />
                                                <span>邀請時間：{new Date(rec.created_at!).toLocaleString('zh-TW')}</span>
                                            </div>
                                        </div>

                                        {/* 右側：操作按鈕 */}
                                        <div className="flex flex-wrap gap-2 sm:justify-end">
                                            {rec.submitted_at && (
                                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                                    <Clock className="h-4 w-4" />
                                                    <span>提交時間：{new Date(rec.submitted_at).toLocaleString('zh-TW')}</span>
                                                </div>
                                            )}

                                            {rec.status === 'pending' && (
                                                <>
                                                    <Button
                                                        onClick={() => {
                                                            setSelectedRecommendation(rec.id);
                                                            loadQuestionnaireTemplate(rec.id);
                                                        }}
                                                        className="bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500"
                                                        size="sm"
                                                    >
                                                        提交推薦函
                                                        {/* todo 提交行為須提示推薦人提交後無法撤回，僅能更新提交方式與內容 */}
                                                    </Button>
                                                    <Button
                                                        variant="outline"
                                                        onClick={() => handleDecline(rec)}
                                                        className="border-red-300 text-red-600 hover:border-red-400 hover:bg-red-50"
                                                        size="sm"
                                                    >
                                                        婉拒邀請
                                                    </Button>
                                                </>
                                            )}

                                            {rec.status === 'submitted' && (
                                                <>
                                                    {rec.pdf_path && (
                                                        <Button
                                                            variant="outline"
                                                            onClick={() => previewPdf(rec.pdf_path!)}
                                                            className="border-blue-300 text-blue-600 hover:border-blue-400 hover:bg-blue-50"
                                                            size="sm"
                                                        >
                                                            檢視推薦函
                                                        </Button>
                                                    )}
                                                    {/* 重新提交按鈕 */}
                                                    <Button
                                                        variant="outline"
                                                        onClick={() => {
                                                            setSelectedRecommendation(rec.id);
                                                            loadQuestionnaireTemplate(rec.id);
                                                        }}
                                                        className="border-green-300 text-green-600 hover:border-green-400 hover:bg-green-50"
                                                        size="sm"
                                                    >
                                                        重新提交
                                                    </Button>
                                                </>
                                            )}

                                            {rec.status === 'declined' && <span className="text-sm text-gray-500 italic">已婉拒此推薦邀請</span>}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}

            {/* 推薦函處理模態框 */}
            {selectedRecommendation && (
                <div className="fixed inset-0 z-50 m-0 flex items-center justify-center bg-black/50 p-4">
                    <Card
                        className={`w-full overflow-y-auto rounded-lg shadow-lg transition-all ${
                            submissionType === 'pdf' ? 'max-w-2xl' : 'max-w-5xl'
                        } max-h-[90vh]`}
                    >
                        <CardHeader>
                            <CardTitle className="text-lg font-semibold">{t('dashboard.recommender.fillRecommendation')}</CardTitle>
                        </CardHeader>

                        <CardContent className="space-y-6">
                            {/* 提交方式選擇 */}
                            {submission_settings && (submission_settings.allow_pdf_upload || submission_settings.allow_questionnaire_submission) && (
                                <div className="space-y-4">
                                    <h3 className="text-lg font-semibold text-gray-900">請選擇提交方式</h3>
                                    <div
                                        className={`grid grid-cols-1 gap-4 ${
                                            submission_settings.allow_pdf_upload && submission_settings.allow_questionnaire_submission
                                                ? 'md:grid-cols-2'
                                                : 'md:grid-cols-1'
                                        }`}
                                    >
                                        {/* PDF上傳選項 */}
                                        {submission_settings.allow_pdf_upload && (
                                            <div
                                                className={`cursor-pointer rounded-lg border-2 p-6 transition-all ${
                                                    submissionType === 'pdf' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                                                }`}
                                                onClick={() => setSubmissionType('pdf')}
                                            >
                                                <div className="space-y-3 text-center">
                                                    <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                                                        <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth={2}
                                                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                                                            />
                                                        </svg>
                                                    </div>
                                                    <h4 className="font-medium text-gray-900">上傳PDF文件</h4>
                                                    <p className="text-sm text-gray-600">直接上傳已完成的推薦函PDF</p>
                                                </div>
                                            </div>
                                        )}

                                        {/* 問卷填寫選項 */}
                                        {submission_settings.allow_questionnaire_submission && (
                                            <div
                                                className={`cursor-pointer rounded-lg border-2 p-6 transition-all ${
                                                    submissionType === 'questionnaire'
                                                        ? 'border-blue-500 bg-blue-50'
                                                        : 'border-gray-200 hover:border-gray-300'
                                                }`}
                                                onClick={() => setSubmissionType('questionnaire')}
                                            >
                                                <div className="space-y-3 text-center">
                                                    <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                                                        <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth={2}
                                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                                            />
                                                        </svg>
                                                    </div>
                                                    <h4 className="font-medium text-gray-900">填寫線上問卷</h4>
                                                    <p className="text-sm text-gray-600">透過系統問卷填寫推薦內容</p>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* PDF 上傳區域 */}
                            {submissionType === 'pdf' && submission_settings?.allow_pdf_upload && (
                                <div className="space-y-4">
                                    <div className="rounded-lg border-2 border-dashed border-gray-300 p-6 text-center transition-colors hover:border-gray-400">
                                        <div className="space-y-4">
                                            <div>
                                                <p className="mb-4 text-sm text-gray-600">請選擇您已完成的推薦函PDF文件</p>
                                                <Input
                                                    id="pdf-upload"
                                                    type="file"
                                                    accept=".pdf"
                                                    onChange={(e) => setPdfFile(e.target.files?.[0] || null)}
                                                    className="file:mr-4 file:rounded-md file:px-4 file:text-sm file:font-medium file:text-blue-700 hover:file:bg-blue-100"
                                                />
                                                <p className="mt-2 text-xs text-gray-500">支援 PDF 格式，檔案大小不超過 10MB</p>
                                            </div>
                                        </div>
                                    </div>
                                    {pdfFile && (
                                        <div className="mt-4">
                                            <PDFPreview file={pdfFile} onRemove={() => setPdfFile(null)} showPreview={true} />
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* 問卷區域 */}
                            {submissionType === 'questionnaire' && submission_settings?.allow_questionnaire_submission && (
                                <div className="space-y-4">
                                    {isLoadingTemplate ? (
                                        <div className="py-12 text-center">
                                            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                                                <svg
                                                    className="h-8 w-8 animate-spin text-green-600"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                                                    />
                                                </svg>
                                            </div>
                                            <p className="text-gray-500">載入問卷模板中...</p>
                                        </div>
                                    ) : questionnaireTemplate ? (
                                        <DynamicQuestionnaire
                                            template={questionnaireTemplate}
                                            initialData={questionnaireData}
                                            onSubmit={handleQuestionnaireSubmit}
                                            onCancel={() => {
                                                setSelectedRecommendation(null);
                                                setQuestionnaireTemplate(null);
                                                setQuestionnaireData({});
                                                setSubmissionType(null);
                                                setPdfFile(null);
                                            }}
                                            isSubmitting={isQuestionnaireSubmitting}
                                            applicantInfo={
                                                selectedRecommendation
                                                    ? (() => {
                                                          const rec = recommendations.find((r) => r.id === selectedRecommendation);
                                                          return rec && rec.applicant?.user
                                                              ? {
                                                                    name: rec.applicant.user.name,
                                                                    department_name: rec.department_name,
                                                                    program_type: rec.program_type,
                                                                }
                                                              : undefined;
                                                      })()
                                                    : undefined
                                            }
                                        />
                                    ) : (
                                        <div className="py-12 text-center">
                                            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                                                <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                                                    />
                                                </svg>
                                            </div>
                                            <p className="text-gray-500">無法載入問卷模板</p>
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* PDF模式的提交按鈕 */}
                            {submissionType === 'pdf' && (
                                <div className="flex justify-end gap-3 border-t pt-6">
                                    <Button
                                        variant="outline"
                                        onClick={() => {
                                            setSelectedRecommendation(null);
                                            setPdfFile(null);
                                            setSubmissionType('pdf');
                                        }}
                                    >
                                        {t('common.cancel')}
                                    </Button>
                                    <Button
                                        onClick={() => {
                                            const rec = recommendations.find((r) => r.id === selectedRecommendation);
                                            if (rec) {
                                                handleSubmitConfirmation(rec, 'pdf');
                                            }
                                        }}
                                        disabled={!pdfFile || isQuestionnaireSubmitting}
                                        className="min-w-[100px]"
                                    >
                                        {isQuestionnaireSubmitting ? '上傳中...' : '上傳PDF'}
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            )}

            {/* 確認視窗 */}
            <ConfirmationDialog
                isOpen={confirmDialog.isOpen}
                onClose={closeConfirmDialog}
                onConfirm={handleConfirmedAction}
                type="warning"
                isLoading={confirmDialog.isLoading}
                title={
                    confirmDialog.type === 'decline'
                        ? `確定要婉拒推薦函邀請嗎？`
                        : confirmDialog.type === 'withdraw'
                          ? `確定要撤回推薦函嗎？`
                          : confirmDialog.type === 'submit'
                            ? `確定要提交推薦函嗎？`
                            : '確認操作'
                }
                message={
                    confirmDialog.type === 'decline'
                        ? '您即將婉拒推薦函邀請。'
                        : confirmDialog.type === 'withdraw'
                          ? '撤回後，推薦函狀態將變為待處理，您可以重新提交。'
                          : confirmDialog.type === 'submit'
                            ? `提交後無法撤回，僅能更新提交方式與內容。請確認所有資訊正確無誤。`
                            : '請確認您的操作。'
                }
                // 考生資訊
                subMessage={
                    confirmDialog.type === 'submit'
                        ? `考生：${recommendations.find((r) => r.id === selectedRecommendation)?.applicant?.user?.name || '未知'}`
                        : undefined
                }
                confirmText={
                    confirmDialog.type === 'decline'
                        ? '婉拒'
                        : confirmDialog.type === 'withdraw'
                          ? '撤回'
                          : confirmDialog.type === 'submit'
                            ? '確定提交'
                            : '確定'
                }
            />
        </div>
    );
}
