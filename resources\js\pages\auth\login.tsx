import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Users, Mail } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

type LoginForm = {
    email: string;
    password: string;
};

interface LoginProps {
    status?: string;
}

// 管理員登入頁面 - 用於系統管理和開發測試
export default function Login({ status }: LoginProps) {
    const { data, setData, post, processing, errors, reset } = useForm<Required<LoginForm>>({
        email: '',
        password: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('login'), {
            onFinish: () => reset('password'),
        });
    };

    return (
        <AuthLayout title="系統登入" description="請輸入帳號密碼登入系統">
            <Head title="管理員登入" />

            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-2">
                        <Label htmlFor="email">電子郵件</Label>
                        <Input
                            id="email"
                            type="email"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            placeholder=""
                        />
                        <InputError message={errors.email} />
                    </div>

                    <div className="grid gap-2">
                        <div className="flex items-center">
                            <Label htmlFor="password">密碼</Label>
                        </div>
                        <Input
                            id="password"
                            type="password"
                            required
                            tabIndex={2}
                            autoComplete="current-password"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            placeholder=""
                        />
                        <InputError message={errors.password} />
                    </div>

                    <Button type="submit" className="w-full" tabIndex={4} disabled={processing}>
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                        登入
                    </Button>
                </div>

                {/* 考生登入說明 - 優化版 */}
                <div className="rounded-lg border border-emerald-200 bg-gradient-to-r from-emerald-50 to-green-50 p-5 shadow-sm">
                    <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-emerald-100">
                                <Users className="h-4 w-4 text-emerald-600" />
                            </div>
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="text-base font-semibold text-emerald-900">考生登入</h3>
                            <p className="mt-2 text-sm leading-relaxed text-emerald-800">
                                請透過
                                <span className="mx-1 inline-flex items-center rounded-md bg-emerald-100 px-2 py-0.5 text-xs font-medium text-emerald-700">
                                    報名系統
                                </span>
                                登入，系統會自動導向推薦函管理頁面。
                            </p>
                        </div>
                    </div>
                </div>

                {/* 推薦人登入說明 - 優化版 */}
                <div className="rounded-lg border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 p-5 shadow-sm">
                    <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                                <Mail className="h-4 w-4 text-blue-600" />
                            </div>
                        </div>
                        <div className="min-w-0 flex-1">
                            <h3 className="text-base font-semibold text-blue-900">推薦人登入</h3>
                            <p className="mt-2 text-sm leading-relaxed text-blue-800">
                                請檢查您的信箱，並從
                                <span className="mx-1 inline-flex items-center rounded-md bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700">
                                    專屬連結
                                </span>
                                進行登入。
                            </p>
                        </div>
                    </div>
                </div>
            </form>

            {status && <div className="mb-4 text-center text-sm font-medium text-green-600">{status}</div>}
        </AuthLayout>
    );
}
