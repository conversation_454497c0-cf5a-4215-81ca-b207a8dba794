<?php

namespace App\Http\Middleware;

use App\Models\RecommendationLetter;
use App\Models\SystemSetting;
use App\Models\SystemLog;
use App\Models\User;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

/**
 * 提醒冷卻時間檢查中間件
 * 
 * 檢查推薦函提醒功能的冷卻時間限制
 */
class CheckReminderCooldown
{
    /**
     * 處理傳入的請求
     *
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        /** @var User|null $user */
        $user = Auth::user();

        // 管理員不受冷卻時間限制
        if ($user && $user->isAdmin()) {
            return $next($request);
        }

        // 只對提醒相關的路由進行檢查
        if (!$request->routeIs('recommendations.remind')) {
            return $next($request);
        }

        try {
            // 取得推薦函ID
            $recommendationId = $request->route('id');
            if (!$recommendationId) {
                return $next($request);
            }

            // 查找推薦函
            $recommendation = RecommendationLetter::find($recommendationId);
            if (!$recommendation) {
                return $next($request);
            }

            // 檢查是否為推薦函的申請人
            if ($user && $user->isApplicant()) {
                $applicant = $user->applicant;
                if (!$applicant || $recommendation->applicant_id !== $applicant->id) {
                    SystemLog::logOperation(
                        SystemLog::ACTION_VIEW,
                        '嘗試對非自己的推薦函發送提醒',
                        [
                            'user_id' => $user->id,
                            'recommendation_id' => $recommendationId,
                            'actual_applicant_id' => $recommendation->applicant_id,
                        ],
                        SystemLog::LEVEL_WARNING
                    );

                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => '您沒有權限對此推薦函發送提醒',
                            'error_code' => 'UNAUTHORIZED_REMINDER'
                        ], 403);
                    }

                    return back()->withErrors([
                        'recommendation' => '您沒有權限對此推薦函發送提醒'
                    ]);
                }
            }

            // 檢查冷卻時間
            $cooldownHours = SystemSetting::getReminderCooldownHours();
            $lastRemindedAt = $recommendation->last_reminded_at;

            if ($lastRemindedAt) {
                $cooldownUntil = $lastRemindedAt->addHours($cooldownHours);
                $now = now();

                if ($now->lt($cooldownUntil)) {
                    $remainingTime = $now->diffInMinutes($cooldownUntil);
                    $remainingHours = floor($remainingTime / 60);
                    $remainingMinutes = $remainingTime % 60;

                    $timeMessage = '';
                    if ($remainingHours > 0) {
                        $timeMessage = "{$remainingHours} 小時 {$remainingMinutes} 分鐘";
                    } else {
                        $timeMessage = "{$remainingMinutes} 分鐘";
                    }

                    SystemLog::logOperation(
                        SystemLog::ACTION_VIEW,
                        '嘗試在冷卻時間內發送提醒',
                        [
                            'user_id' => $user?->id,
                            'recommendation_id' => $recommendationId,
                            'last_reminded_at' => $lastRemindedAt->toDateTimeString(),
                            'cooldown_until' => $cooldownUntil->toDateTimeString(),
                            'remaining_minutes' => $remainingTime,
                        ],
                        SystemLog::LEVEL_WARNING
                    );

                    if ($request->expectsJson()) {
                        return response()->json([
                            'success' => false,
                            'message' => "提醒功能冷卻中，請等待 {$timeMessage} 後再試",
                            'error_code' => 'REMINDER_COOLDOWN',
                            'cooldown_until' => $cooldownUntil->toISOString(),
                            'remaining_minutes' => $remainingTime,
                        ], 429);
                    }

                    return back()->withErrors([
                        'reminder' => "提醒功能冷卻中，請等待 {$timeMessage} 後再試"
                    ]);
                }
            }

            // 記錄通過冷卻檢查的日誌
            SystemLog::logOperation(
                SystemLog::ACTION_VIEW,
                '通過提醒冷卻時間檢查',
                [
                    'user_id' => $user?->id,
                    'recommendation_id' => $recommendationId,
                    'last_reminded_at' => $lastRemindedAt?->toDateTimeString(),
                    'cooldown_hours' => $cooldownHours,
                ]
            );
        } catch (\Exception $e) {
            SystemLog::logError(
                SystemLog::ACTION_VIEW,
                '檢查提醒冷卻時間時發生錯誤',
                $e,
                [
                    'user_id' => $user?->id,
                    'recommendation_id' => $request->route('id'),
                ]
            );

            // 發生錯誤時允許繼續，但記錄錯誤
            return $next($request);
        }

        return $next($request);
    }
}
