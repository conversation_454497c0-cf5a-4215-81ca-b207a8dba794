<?php

namespace Tests\Feature;

use App\Jobs\ProcessPdfMergeJob;
use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use App\Models\Applicant;
use App\Models\User;
use App\Services\FilePackagingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PdfCompressionApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 設定測試存儲
        Storage::fake('local');
        
        // 創建測試用戶和申請人
        $this->createTestData();
    }

    protected function createTestData(): void
    {
        // 創建申請人
        $applicant = Applicant::factory()->create([
            'exam_id' => '2',
            'exam_year' => 114,
            'external_autono' => '349'
        ]);

        // 創建推薦函
        $recommendation = RecommendationLetter::factory()->create([
            'applicant_id' => $applicant->id,
            'exam_id' => '2',
            'exam_year' => 114,
            'external_autono' => '349',
            'status' => RecommendationLetter::STATUS_SUBMITTED,
            'pdf_path' => 'recommendations/2/114/349/1.pdf'
        ]);

        // 創建假的 PDF 檔案
        Storage::disk('local')->put(
            'recommendations/2/114/349/1.pdf',
            'fake pdf content'
        );
    }

    /** @test */
    public function it_can_start_compression_task()
    {
        $response = $this->postJson('/api/pdf-merge/start-merge', [
            'exam_id' => '2',
            'exam_year' => 114
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'message',
                    'data' => [
                        'task_id',
                        'status',
                        'estimated_completion'
                    ]
                ]);

        $this->assertDatabaseHas('pdf_merge_tasks', [
            'status' => PdfMergeTask::STATUS_PROCESSING
        ]);
    }

    /** @test */
    public function it_can_check_task_status()
    {
        $task = PdfMergeTask::createTask([
            'exam_id' => '2',
            'exam_year' => 114,
            'requested_at' => now()->toISOString(),
            'client_ip' => '127.0.0.1'
        ]);

        $response = $this->getJson("/api/pdf-merge/merge-status?task_id={$task->task_id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'data' => [
                        'task_id',
                        'status',
                        'progress'
                    ]
                ]);
    }

    /** @test */
    public function it_can_process_compression_job()
    {
        $task = PdfMergeTask::createTask([
            'exam_id' => '2',
            'exam_year' => 114,
            'requested_at' => now()->toISOString(),
            'client_ip' => '127.0.0.1'
        ]);

        $job = new ProcessPdfMergeJob($task->task_id, [
            'exam_id' => '2',
            'exam_year' => 114
        ]);

        $job->handle();

        $task->refresh();

        $this->assertEquals(PdfMergeTask::STATUS_READY, $task->status);
        $this->assertNotNull($task->zip_file_path);
        $this->assertTrue(Storage::disk('local')->exists($task->zip_file_path));
    }

    /** @test */
    public function it_can_create_direct_zip_package()
    {
        $files = [
            [
                'source_path' => storage_path('app/recommendations/2/114/349/1.pdf'),
                'zip_path' => '2/114/349.pdf',
                'applicant_id' => 1,
                'autono' => '349'
            ]
        ];

        // 確保源檔案存在
        Storage::disk('local')->put('recommendations/2/114/349/1.pdf', 'test content');

        $packagingService = new FilePackagingService();
        $zipPath = $packagingService->createDirectZipPackage(
            $files,
            'test_task_123',
            ['exam_id' => '2', 'exam_year' => 114]
        );

        $this->assertNotNull($zipPath);
        $this->assertTrue(Storage::disk('local')->exists($zipPath));

        // 驗證 ZIP 檔案內容
        $fullZipPath = storage_path('app/' . $zipPath);
        $zip = new \ZipArchive();
        $this->assertTrue($zip->open($fullZipPath) === TRUE);
        
        // 檢查檔案是否存在於 ZIP 中
        $this->assertNotFalse($zip->locateName('2/114/349.pdf'));
        $this->assertNotFalse($zip->locateName('README.txt'));
        
        $zip->close();
    }

    /** @test */
    public function it_handles_no_recommendations_gracefully()
    {
        $response = $this->postJson('/api/pdf-merge/start-merge', [
            'exam_id' => '999',
            'exam_year' => 999
        ]);

        $response->assertStatus(404)
                ->assertJson([
                    'status' => 'error',
                    'message' => '沒有找到符合條件的已提交推薦函'
                ]);
    }

    /** @test */
    public function it_validates_required_parameters()
    {
        $response = $this->postJson('/api/pdf-merge/start-merge', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['exam_id', 'exam_year']);
    }

    /** @test */
    public function it_can_download_completed_task()
    {
        $task = PdfMergeTask::createTask([
            'exam_id' => '2',
            'exam_year' => 114,
            'requested_at' => now()->toISOString(),
            'client_ip' => '127.0.0.1'
        ]);

        // 模擬完成的任務
        $zipPath = 'pdf_merges/test_file.zip';
        Storage::disk('local')->put($zipPath, 'fake zip content');
        
        $task->markAsReady($zipPath, "/api/pdf-merge/download?task_id={$task->task_id}");

        $response = $this->get("/api/pdf-merge/download?task_id={$task->task_id}");

        $response->assertStatus(200);
        $this->assertEquals('application/zip', $response->headers->get('content-type'));
    }

    /** @test */
    public function it_handles_expired_tasks()
    {
        $task = PdfMergeTask::createTask([
            'exam_id' => '2',
            'exam_year' => 114,
            'requested_at' => now()->toISOString(),
            'client_ip' => '127.0.0.1'
        ]);

        // 設定任務為過期
        $task->update(['expires_at' => now()->subHour()]);

        $response = $this->get("/api/pdf-merge/download?task_id={$task->task_id}");

        $response->assertStatus(400)
                ->assertJson([
                    'status' => 'error',
                    'message' => '檔案尚未準備好或已過期'
                ]);
    }
}
