import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Download, Eye, LogIn, User, Shield, Users, Trash2, Monitor, Smartphone, Globe, Clock } from 'lucide-react';
import { useState } from 'react';
import { router } from '@inertiajs/react';
import DataTable from '@/components/admin/DataTable';

interface LoginLog {
    id: number;
    user_id?: number;
    user_type: string;
    email: string;
    ip_address: string;
    user_agent: string;
    login_method: string;
    status: string;
    failure_reason?: string;
    location?: string;
    device_info?: string;
    session_id?: string;
    login_at: string;
    created_at: string;
    user?: {
        id: number;
        name: string;
        email: string;
        role: string;
    };
}

interface LoginLogsProps {
    loginLogs: {
        data: LoginLog[];
        current_page: number;
        last_page: number;
        from: number;
        to: number;
        total: number;
    };
}

export default function LoginLogs({ loginLogs }: LoginLogsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [roleFilter, setRoleFilter] = useState('all');
    const [methodFilter, setMethodFilter] = useState('all');
    const [statusFilter, setStatusFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '登入日誌', href: '/admin/login-logs' },
    ];

    // 格式化日期
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('zh-TW');
    };

    // 獲取瀏覽器資訊
    const getBrowserInfo = (userAgent: string) => {
        if (userAgent.includes('Chrome')) return { name: 'Chrome', icon: <Monitor className="h-3 w-3" /> };
        if (userAgent.includes('Firefox')) return { name: 'Firefox', icon: <Monitor className="h-3 w-3" /> };
        if (userAgent.includes('Safari')) return { name: 'Safari', icon: <Monitor className="h-3 w-3" /> };
        if (userAgent.includes('Edge')) return { name: 'Edge', icon: <Monitor className="h-3 w-3" /> };
        if (userAgent.includes('Mobile')) return { name: '行動裝置', icon: <Smartphone className="h-3 w-3" /> };
        return { name: '未知瀏覽器', icon: <Monitor className="h-3 w-3" /> };
    };

    // 獲取登入方法徽章
    const getMethodBadge = (method: string) => {
        switch (method) {
            case 'password':
                return (
                    <Badge variant="outline" className="text-blue-700">
                        密碼登入
                    </Badge>
                );
            case 'jwt':
                return (
                    <Badge variant="outline" className="text-green-700">
                        JWT Token
                    </Badge>
                );
            case 'oauth':
                return (
                    <Badge variant="outline" className="text-purple-700">
                        OAuth
                    </Badge>
                );
            case 'sso':
                return (
                    <Badge variant="outline" className="text-orange-700">
                        SSO
                    </Badge>
                );
            default:
                return <Badge variant="outline">{method}</Badge>;
        }
    };

    // 獲取狀態徽章
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'success':
                return (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                        成功
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                        失敗
                    </Badge>
                );
            case 'blocked':
                return (
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                        封鎖
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{status}</Badge>;
        }
    };

    // 獲取用戶類型徽章
    const getUserTypeBadge = (userType: string) => {
        switch (userType) {
            case 'admin':
                return (
                    <Badge variant="secondary" className="bg-red-100 text-red-800">
                        <Shield className="mr-1 h-3 w-3" />
                        管理員
                    </Badge>
                );
            case 'applicant':
                return (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                        <User className="mr-1 h-3 w-3" />
                        申請人
                    </Badge>
                );
            case 'recommender':
                return (
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                        <Users className="mr-1 h-3 w-3" />
                        推薦人
                    </Badge>
                );
            default:
                return <Badge variant="secondary">{userType}</Badge>;
        }
    };

    // 匯出登入記錄
    const handleExportLogs = () => {
        const params = new URLSearchParams();
        if (roleFilter !== 'all') params.append('role', roleFilter);
        if (methodFilter !== 'all') params.append('method', methodFilter);
        if (statusFilter !== 'all') params.append('status', statusFilter);
        if (searchTerm) params.append('search', searchTerm);

        window.open(`/admin/login-logs/export?${params.toString()}`, '_blank');
    };

    // 清理舊記錄
    const handleCleanupLogs = () => {
        if (confirm('確定要清理 90 天前的舊登入記錄嗎？此操作無法復原。')) {
            router.post(
                '/admin/login-logs/cleanup',
                { days: 90 },
                {
                    onSuccess: () => {
                        alert('舊記錄已清理完成');
                        router.reload();
                    },
                    onError: () => {
                        alert('清理失敗，請重試');
                    },
                },
            );
        }
    };

    // 查看詳情
    const handleViewDetail = (log: LoginLog) => {
        router.visit(route('admin.login-logs.show', log.id));
    };

    // 定義表格欄位
    const columns = [
        {
            key: 'login_at',
            label: '登入時間',
            sortable: true,
            render: (value: string) => (
                <div className="text-sm">
                    <div>{formatDate(value)}</div>
                </div>
            ),
        },
        {
            key: 'user',
            label: '使用者',
            render: (value: any, row: LoginLog) => (
                <div>
                    <div className="font-medium">{value?.name || row.email}</div>
                    <div className="text-sm text-gray-500">{row.email}</div>
                    <div className="mt-1">{getUserTypeBadge(row.user_type)}</div>
                </div>
            ),
        },
        {
            key: 'ip_address',
            label: 'IP 位址',
            render: (value: string, row: LoginLog) => (
                <div>
                    <div className="font-mono text-sm">{value}</div>
                    {row.location && (
                        <div className="mt-1 flex items-center text-xs text-gray-500">
                            <Globe className="mr-1 h-3 w-3" />
                            {row.location}
                        </div>
                    )}
                </div>
            ),
        },
        {
            key: 'user_agent',
            label: '裝置/瀏覽器',
            render: (value: string) => {
                const browserInfo = getBrowserInfo(value);
                return (
                    <div className="flex items-center">
                        {browserInfo.icon}
                        <span className="ml-1 text-sm">{browserInfo.name}</span>
                    </div>
                );
            },
        },
        {
            key: 'login_method',
            label: '登入方式',
            render: (value: string) => getMethodBadge(value),
        },
        {
            key: 'status',
            label: '狀態',
            render: (value: string, row: LoginLog) => (
                <div>
                    {getStatusBadge(value)}
                    {row.failure_reason && (
                        <div className="mt-1 text-xs text-red-500" title={row.failure_reason}>
                            {row.failure_reason.length > 20 ? row.failure_reason.substring(0, 20) + '...' : row.failure_reason}
                        </div>
                    )}
                </div>
            ),
        },
    ];

    // 定義操作按鈕
    const actions = [
        {
            key: 'view',
            label: '查看',
            icon: <Eye className="h-3 w-3" />,
            onClick: handleViewDetail,
            variant: 'outline' as const,
        },
    ];

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="登入日誌" description="查看和管理使用者登入記錄和安全日誌">
            <Head title="登入日誌" />

            <div className="space-y-6 p-6">
                {/* 搜尋和過濾 */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <LogIn className="h-5 w-5" />
                            搜尋和過濾
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-4">
                            <div className="min-w-64 flex-1">
                                <Input
                                    placeholder="搜尋使用者名稱、Email 或 IP..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <div className="min-w-32">
                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="狀態" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">所有狀態</SelectItem>
                                        <SelectItem value="success">成功</SelectItem>
                                        <SelectItem value="failed">失敗</SelectItem>
                                        <SelectItem value="blocked">封鎖</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="min-w-32">
                                <Select value={roleFilter} onValueChange={setRoleFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="用戶類型" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">所有類型</SelectItem>
                                        <SelectItem value="admin">管理員</SelectItem>
                                        <SelectItem value="applicant">申請人</SelectItem>
                                        <SelectItem value="recommender">推薦人</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="min-w-32">
                                <Select value={methodFilter} onValueChange={setMethodFilter}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="登入方式" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">所有方式</SelectItem>
                                        <SelectItem value="password">密碼登入</SelectItem>
                                        <SelectItem value="jwt">JWT Token</SelectItem>
                                        <SelectItem value="oauth">OAuth</SelectItem>
                                        <SelectItem value="sso">SSO</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <Button variant="outline" onClick={handleExportLogs}>
                                <Download className="mr-2 h-4 w-4" />
                                匯出
                            </Button>
                            <Button variant="outline" onClick={handleCleanupLogs}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                清理
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* 統計資訊 */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <LogIn className="h-8 w-8 text-blue-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">總計</p>
                                    <p className="text-2xl font-bold text-gray-900">{loginLogs.total}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Shield className="h-8 w-8 text-green-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">成功登入</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {loginLogs.data.filter((log) => log.status === 'success').length}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <User className="h-8 w-8 text-red-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">失敗登入</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {loginLogs.data.filter((log) => log.status === 'failed').length}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center">
                                <Users className="h-8 w-8 text-purple-600" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">獨特用戶</p>
                                    <p className="text-2xl font-bold text-gray-900">{new Set(loginLogs.data.map((log) => log.email)).size}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* 資料表格 */}
                <DataTable
                    data={loginLogs.data.filter((log) => {
                        const matchesSearch =
                            log.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            log.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            log.ip_address?.includes(searchTerm);

                        const matchesStatus = statusFilter === 'all' || log.status === statusFilter;
                        const matchesRole = roleFilter === 'all' || log.user_type === roleFilter;
                        const matchesMethod = methodFilter === 'all' || log.login_method === methodFilter;

                        return matchesSearch && matchesStatus && matchesRole && matchesMethod;
                    })}
                    columns={columns}
                    actions={actions}
                    title="登入日誌列表"
                    description={`顯示第 ${loginLogs.from} 到 ${loginLogs.to} 筆，共 ${loginLogs.total} 筆記錄`}
                    emptyMessage="沒有找到符合條件的登入記錄"
                    exportable={true}
                    onExport={handleExportLogs}
                />
            </div>
        </AdminLayout>
    );
}
