
[2025-07-25 10:36:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/health","method":"GET"} 
[2025-07-25 10:36:24] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 10:36:24] local.ERROR: 啟動PDF合併任務失敗 {"error":"SQLSTATE[HY000]: General error: 1 no such table: jobs (Connection: sqlite, SQL: insert into \"jobs\" (\"queue\", \"attempts\", \"reserved_at\", \"available_at\", \"created_at\", \"payload\") values (default, 0, ?, **********, **********, {\"uuid\":\"99fd663d-f9b9-4f32-8db1-f20c1da5c84b\",\"displayName\":\"App\\\\Jobs\\\\ProcessPdfMergeJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":3,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":1800,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\ProcessPdfMergeJob\",\"command\":\"O:27:\\\"App\\\\Jobs\\\\ProcessPdfMergeJob\\\":2:{s:9:\\\"\\u0000*\\u0000taskId\\\";s:33:\\\"merge_4rRsUFgr4d5G4ji6_**********\\\";s:13:\\\"\\u0000*\\u0000parameters\\\";a:4:{s:7:\\\"exam_id\\\";s:4:\\\"test\\\";s:9:\\\"exam_year\\\";i:2024;s:12:\\\"requested_at\\\";s:27:\\\"2025-07-25T02:36:24.190369Z\\\";s:9:\\\"client_ip\\\";s:9:\\\"127.0.0.1\\\";}}\"},\"createdAt\":**********,\"delay\":null}))","request_data":{"exam_id":"test","exam_year":2024},"client_ip":"127.0.0.1"} 
[2025-07-25 10:40:56] local.ERROR: SQLSTATE[HY000]: General error: 1 table "pdf_merge_tasks" already exists (Connection: sqlite, SQL: create table "pdf_merge_tasks" ("id" integer primary key autoincrement not null, "created_at" datetime, "updated_at" datetime)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists (Connection: sqlite, SQL: create table \"pdf_merge_tasks\" (\"id\" integer primary key autoincrement not null, \"created_at\" datetime, \"updated_at\" datetime)) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_07_25_103155_create_pdf_merge_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_25_1031...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_25_1031...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"p...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"p...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_07_25_103155_create_pdf_merge_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_25_1031...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_25_1031...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-25 10:41:04] local.ERROR: SQLSTATE[HY000]: General error: 1 table "pdf_merge_tasks" already exists (Connection: sqlite, SQL: create table "pdf_merge_tasks" ("id" integer primary key autoincrement not null, "created_at" datetime, "updated_at" datetime)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists (Connection: sqlite, SQL: create table \"pdf_merge_tasks\" (\"id\" integer primary key autoincrement not null, \"created_at\" datetime, \"updated_at\" datetime)) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_07_25_103155_create_pdf_merge_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_25_1031...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_25_1031...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"p...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"p...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_07_25_103155_create_pdf_merge_tasks_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_25_1031...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_25_1031...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-25 10:41:44] local.ERROR: SQLSTATE[HY000]: General error: 1 table "pdf_merge_tasks" already exists (Connection: sqlite, SQL: create table "pdf_merge_tasks" ("id" integer primary key autoincrement not null, "task_id" varchar not null, "status" varchar check ("status" in ('processing', 'ready', 'failed', 'expired')) not null default 'processing', "progress" integer not null default '0', "parameters" text, "download_url" varchar, "zip_file_path" varchar, "total_files" integer not null default '0', "processed_files" integer not null default '0', "error_message" text, "expires_at" datetime, "created_at" datetime, "updated_at" datetime)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists (Connection: sqlite, SQL: create table \"pdf_merge_tasks\" (\"id\" integer primary key autoincrement not null, \"task_id\" varchar not null, \"status\" varchar check (\"status\" in ('processing', 'ready', 'failed', 'expired')) not null default 'processing', \"progress\" integer not null default '0', \"parameters\" text, \"download_url\" varchar, \"zip_file_path\" varchar, \"total_files\" integer not null default '0', \"processed_files\" integer not null default '0', \"error_message\" text, \"expires_at\" datetime, \"created_at\" datetime, \"updated_at\" datetime)) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_06_24_000001_create_recommendation_system_tables.php(270): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_24_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_24_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"pdf_merge_tasks\" already exists at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"p...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"p...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"p...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"p...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"p...')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('pdf_merge_tasks', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\2025_06_24_000001_create_recommendation_system_tables.php(270): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_24_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_24_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#39 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}
"} 
[2025-07-25 10:42:44] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-25 10:42:51] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T02:42:51.644022Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T02:42:51.645781Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T02:42:51.645917Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T02:42:51.645997Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T02:42:51.646069Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T02:42:51.646137Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T02:42:51.646206Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T02:42:51.646275Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T02:42:51.646343Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T02:42:51.646440Z"}]} 
[2025-07-25 10:42:51] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T02:42:51.644022Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T02:42:51.645781Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T02:42:51.645917Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T02:42:51.645997Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T02:42:51.646069Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T02:42:51.646137Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T02:42:51.646206Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T02:42:51.646275Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T02:42:51.646343Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T02:42:51.646440Z"}]}}}} 
[2025-07-25 10:48:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 10:48:12] local.INFO: PDF合併任務已啟動 {"task_id":"merge_aVSEus5bLuJhAFnE_1753411692","parameters":{"requested_at":"2025-07-25T02:48:12.028980Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 10:49:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 10:50:22] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 10:50:22] local.INFO: PDF合併任務已啟動 {"task_id":"merge_ZxSiS5LXc71iKnYT_1753411822","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T02:50:22.889541Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 10:53:03] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 10:56:51] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 10:58:46] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-25 10:58:53] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T02:58:53.208762Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T02:58:53.210391Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T02:58:53.210482Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T02:58:53.210556Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T02:58:53.210623Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T02:58:53.210686Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T02:58:53.210749Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T02:58:53.210809Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T02:58:53.210870Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T02:58:53.210930Z"}]} 
[2025-07-25 10:58:53] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T02:58:53.208762Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T02:58:53.210391Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T02:58:53.210482Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T02:58:53.210556Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T02:58:53.210623Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T02:58:53.210686Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T02:58:53.210749Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T02:58:53.210809Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T02:58:53.210870Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T02:58:53.210930Z"}]}}}} 
[2025-07-25 10:58:56] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 10:59:16] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 10:59:23] local.INFO: 建立新使用者帳號 {"user_id":2,"email":"<EMAIL>","role":"applicant"} 
[2025-07-25 10:59:23] local.INFO: 建立新考生記錄 {"user_id":2,"applicant_id":1,"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":"114","exam_id":"2"} 
[2025-07-25 10:59:23] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 10:59:24] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 10:59:53] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 11:07:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:07:23] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:07:26] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:07:51] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:07:51] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:07:53] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:07:53] local.INFO: PDF合併任務已啟動 {"task_id":"merge_mnrVaasUUBGZYhY8_1753412873","parameters":{"exam_id":"CS001","exam_year":113,"requested_at":"2025-07-25T03:07:53.424890Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 11:07:55] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:08:40] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:08:41] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:08:44] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:08:44] local.INFO: PDF合併任務已啟動 {"task_id":"merge_EokINRYVHYMM0Vzk_1753412924","parameters":{"exam_id":"CS001","exam_year":113,"requested_at":"2025-07-25T03:08:44.462458Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 11:08:46] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:08:48] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:08:50] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:08:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:08:54] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:08:57] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:08:59] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:09:01] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:09:03] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:09:05] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:09:19] local.INFO: 開始處理PDF合併任務 {"task_id":"merge_mnrVaasUUBGZYhY8_1753412873","parameters":{"exam_id":"CS001","exam_year":113,"requested_at":"2025-07-25T03:07:53.424890Z","client_ip":"127.0.0.1"}} 
[2025-07-25 11:09:22] local.INFO: 考生PDF合併完成 {"applicant_id":2,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/applicant_2_merged_6882f561f0676.pdf","file_count":1,"bookmark_count":1} 
[2025-07-25 11:09:22] local.INFO: 考生PDF合併完成 {"applicant_id":3,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/applicant_3_merged_6882f5622a984.pdf","file_count":1,"bookmark_count":1} 
[2025-07-25 11:09:22] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/applicant_2_merged_6882f561f0676.pdf","zip_name":"applicant_2_merged.pdf"} 
[2025-07-25 11:09:22] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/applicant_3_merged_6882f5622a984.pdf","zip_name":"applicant_3_merged.pdf"} 
[2025-07-25 11:09:22] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-25 11:09:22] local.INFO: ZIP檔案創建成功 {"zip_path":"pdf_merges/merged_recommendations_exam_CS001_year_113_2025-07-25_11-09-22_merge_mnrVaasUUBGZYhY8_1753412873.zip","added_files":2,"total_files":2,"task_id":"merge_mnrVaasUUBGZYhY8_1753412873"} 
[2025-07-25 11:09:22] local.INFO: PDF合併任務完成 {"task_id":"merge_mnrVaasUUBGZYhY8_1753412873","merged_files":2,"zip_file":"pdf_merges/merged_recommendations_exam_CS001_year_113_2025-07-25_11-09-22_merge_mnrVaasUUBGZYhY8_1753412873.zip"} 
[2025-07-25 11:12:09] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:12:45] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 11:16:22] local.INFO: 開始同步招生期間資料 {"api_url":"http://localhost:18001/index.php/api/v1/recommendation_system/sync_exam_period"} 
[2025-07-25 11:16:29] local.INFO: 招生期間資料已保存 {"periods_count":10,"data":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T03:16:29.201617Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T03:16:29.202779Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T03:16:29.202881Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T03:16:29.202956Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T03:16:29.203027Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T03:16:29.203096Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T03:16:29.203165Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T03:16:29.203233Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T03:16:29.203305Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T03:16:29.203406Z"}]} 
[2025-07-25 11:16:29] local.INFO: 系統初始化時成功同步外部資料 {"success":true,"message":"系統設定同步完成","results":{"exam_periods":{"success":true,"message":"招生期間資料同步成功","data":{"updated_count":10,"processed_periods":[{"exam_id":"2","exam_name":"碩士班甄試招生(測試)","app_date1_start":"2025/07/01 09:00:00","app_date1_end":"2025/08/30 17:00:00","synced_at":"2025-07-25T03:16:29.201617Z"},{"exam_id":"K","exam_name":"技優甄審指定項目甄審報名(測試)","app_date1_start":"2025/06/05 10:00:00","app_date1_end":"2025/06/10 17:00:00","synced_at":"2025-07-25T03:16:29.202779Z"},{"exam_id":"E","exam_name":"單獨招收身心障礙學生考試(測試)","app_date1_start":"2025/04/28 09:00:00","app_date1_end":"2025/05/09 17:00:00","synced_at":"2025-07-25T03:16:29.202881Z"},{"exam_id":"J","exam_name":"新住民入學招生(測試)","app_date1_start":"2025/04/21 09:00:00","app_date1_end":"2025/05/02 17:00:00","synced_at":"2025-07-25T03:16:29.202956Z"},{"exam_id":"7","exam_name":"大學申請入學指定項目甄試(測試)","app_date1_start":"2025/04/08 10:00:00","app_date1_end":"2025/05/01 17:00:00","synced_at":"2025-07-25T03:16:29.203027Z"},{"exam_id":"1","exam_name":"博士班考試招生(測試)","app_date1_start":"2025/03/18 09:00:00","app_date1_end":"2025/04/08 17:00:00","synced_at":"2025-07-25T03:16:29.203096Z"},{"exam_id":"P","exam_name":"碩士在職專班招生(測試)","app_date1_start":"2025/02/18 09:00:00","app_date1_end":"2025/03/25 17:00:00","synced_at":"2025-07-25T03:16:29.203165Z"},{"exam_id":"3","exam_name":"碩士班考試招生(測試)","app_date1_start":"2025/01/03 09:00:00","app_date1_end":"2025/01/22 17:00:00","synced_at":"2025-07-25T03:16:29.203233Z"},{"exam_id":"B","exam_name":"原住民專班招生(測試)","app_date1_start":"2024/12/03 09:00:00","app_date1_end":"2025/02/27 17:00:00","synced_at":"2025-07-25T03:16:29.203305Z"},{"exam_id":"I","exam_name":"特殊選才單獨招生(測試)","app_date1_start":"2024/10/25 09:00:00","app_date1_end":"2024/11/11 17:00:00","synced_at":"2025-07-25T03:16:29.203406Z"}]}}}} 
[2025-07-25 11:17:52] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 11:17:58] local.INFO: 建立新使用者帳號 {"user_id":2,"email":"<EMAIL>","role":"applicant"} 
[2025-07-25 11:17:58] local.INFO: 建立新考生記錄 {"user_id":2,"applicant_id":1,"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":"114","exam_id":"2"} 
[2025-07-25 11:17:58] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 11:18:39] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/auth-from-external","method":"GET"} 
[2025-07-25 11:18:45] local.INFO: 考生eapapi登入成功 {"external_uid":"ZmOnRv1mNU6+zsms5A1fCg==","exam_year":114,"exam_id":"2","user_id":2,"applicant_id":1,"ip":"127.0.0.1"} 
[2025-07-25 11:18:53] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T03:18:53.946657Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 11:18:53] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-25 11:18:53] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"吳宗霖","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-25 11:18:53] local.INFO: 建立新的推薦人使用者帳號 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖"} 
[2025-07-25 11:18:53] local.INFO: 嘗試新增推薦人資料 {"user_id":3,"email":"<EMAIL>","name":"吳宗霖","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-25 11:18:53] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753413533} 
[2025-07-25 11:18:53] local.INFO: 建立新的推薦人資料 {"recommender_id":1,"user_id":3,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-25 11:18:54] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 11:18:54] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":1,"recommender_email":"<EMAIL>","email_log_id":1} 
[2025-07-25 11:18:59] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T03:18:59.359737Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 11:18:59] local.INFO: 【Middleware】考生存取檢查中間件觸發 {"user":2} 
[2025-07-25 11:18:59] local.INFO: 考生嘗試建立推薦函: {"user_id":2,"applicant_id":1,"request_data":{"recommender_email":"<EMAIL>","recommender_name":"Mike","recommender_title":null,"recommender_phone":null,"recommender_department":null,"department_name":"機械工程學系碩士班","program_type":"碩士班甄試招生(測試)","external_autono":348}} 
[2025-07-25 11:18:59] local.INFO: 建立新的推薦人使用者帳號 {"user_id":4,"email":"<EMAIL>","name":"Mike"} 
[2025-07-25 11:18:59] local.INFO: 嘗試新增推薦人資料 {"user_id":4,"email":"<EMAIL>","name":"Mike","title":"","department":"","phone":"","login_token":"尚未生成","exam_year":114,"exam_id":"2"} 
[2025-07-25 11:18:59] local.DEBUG: Generated secure login token {"token_length":64,"timestamp":1753413539} 
[2025-07-25 11:18:59] local.INFO: 建立新的推薦人資料 {"recommender_id":2,"user_id":4,"email":"<EMAIL>","exam_year":114,"exam_id":"2"} 
[2025-07-25 11:18:59] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函邀請 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 11:18:59] local.INFO: 推薦函邀請信發送成功 {"recommendation_id":2,"recommender_email":"<EMAIL>","email_log_id":2} 
[2025-07-25 11:19:16] local.INFO: 推薦人登入成功 {"recommender_id":1,"user_id":3,"token":"e328ff3e05ef64ad2ba2ffaad7893c96823c159be4405f3f3f57b0da9e6aa177","ip":"127.0.0.1"} 
[2025-07-25 11:20:10] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T03:20:10.536396Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 11:20:10] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":3} 
[2025-07-25 11:20:10] local.INFO: PDF檔案已存儲 {"recommendation_id":1,"file_path":"recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","file_size":47140,"original_name":"下學期課表.pdf"} 
[2025-07-25 11:20:10] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"1","file_path":"recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","file_size":47140} 
[2025-07-25 11:20:10] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 11:20:10] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":1,"applicant_email":"<EMAIL>","email_log_id":3} 
[2025-07-25 11:20:10] local.INFO: 推薦函提交成功 {"recommendation_id":"1","submission_type":"pdf","user_id":3} 
[2025-07-25 11:20:12] local.ERROR: PDF預覽失敗 {"user_id":3,"file_path":"recommendations/2/114/348/1_2025-07-25_11-20-10.pdf","error":"無權限訪問此檔案"} 
[2025-07-25 11:20:16] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 11:20:22] local.INFO: 推薦人登入成功 {"recommender_id":2,"user_id":4,"token":"a6c7327a101b1ea4004662da072c64e47c83d1eb7db8089448d754eba2e3ffeb","ip":"127.0.0.1"} 
[2025-07-25 11:20:34] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T03:20:34.783103Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 11:20:34] local.INFO: 【Middleware】推薦人存取檢查中間件觸發 {"user":4} 
[2025-07-25 11:20:34] local.INFO: PDF檔案已存儲 {"recommendation_id":2,"file_path":"recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","file_size":488789,"original_name":"Group 1.pdf"} 
[2025-07-25 11:20:34] local.INFO: 推薦函 PDF 上傳成功 {"recommendation_id":"2","file_path":"recommendations/2/114/348/2_2025-07-25_11-20-34.pdf","file_size":488789} 
[2025-07-25 11:20:35] local.INFO: 外部 API 郵件發送成功 {"recipient":"<EMAIL>","subject":"推薦函已提交通知 – 碩士班甄試招生(測試)（機械工程學系碩士班）","api_response":"{\"status\":\"success\",\"detail\":\"Message has been sent\"}"} 
[2025-07-25 11:20:35] local.INFO: 推薦函提交通知信發送成功 {"recommendation_id":2,"applicant_email":"<EMAIL>","email_log_id":4} 
[2025-07-25 11:20:35] local.INFO: 推薦函提交成功 {"recommendation_id":"2","submission_type":"pdf","user_id":4} 
[2025-07-25 11:21:38] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:22:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:22:35] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 11:23:01] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 13:05:27] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 13:07:19] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 13:11:51] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 13:12:43] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 13:12:43] local.INFO: PDF合併任務已啟動 {"task_id":"merge_vHcKJrxykOTX3GHk_1753420363","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T05:12:43.946402Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 13:17:02] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:17:58] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:25] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:27] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:28] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:29] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:30] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:31] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:32] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:33] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:34] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:18:35] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"curl/8.13.0","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:20:16] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: failed_jobs (Connection: sqlite, SQL: select * from "failed_jobs" order by "id" desc) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: failed_jobs (Connection: sqlite, SQL: select * from \"failed_jobs\" order by \"id\" desc) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Failed\\DatabaseUuidFailedJobProvider.php(91): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListFailedCommand.php(57): Illuminate\\Queue\\Failed\\DatabaseUuidFailedJobProvider->all()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListFailedCommand.php(41): Illuminate\\Queue\\Console\\ListFailedCommand->getFailedJobs()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\ListFailedCommand->handle()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\ListFailedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#23 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: failed_jobs at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from \"...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from \"...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from \"...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from \"...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select('select * from \"...', Array, true)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Failed\\DatabaseUuidFailedJobProvider.php(91): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListFailedCommand.php(57): Illuminate\\Queue\\Failed\\DatabaseUuidFailedJobProvider->all()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListFailedCommand.php(41): Illuminate\\Queue\\Console\\ListFailedCommand->getFailedJobs()
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\ListFailedCommand->handle()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\ListFailedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#25 {main}
"} 
[2025-07-25 13:20:53] local.ERROR: SQLSTATE[HY000]: General error: 1 table "jobs" already exists (Connection: sqlite, SQL: create table "jobs" ("id" integer primary key autoincrement not null, "queue" varchar not null, "payload" text not null, "attempts" integer not null, "reserved_at" integer, "available_at" integer not null, "created_at" integer not null)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"jobs\" already exists (Connection: sqlite, SQL: create table \"jobs\" (\"id\" integer primary key autoincrement not null, \"queue\" varchar not null, \"payload\" text not null, \"attempts\" integer not null, \"reserved_at\" integer, \"available_at\" integer not null, \"created_at\" integer not null)) at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"j...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"j...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"j...')
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('jobs', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\0001_01_01_000001_create_jobs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 2, false)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table \"jobs\" already exists at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table \"j...')
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table \"j...', Array)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table \"j...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table \"j...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table \"j...')
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('jobs', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\database\\migrations\\0001_01_01_000001_create_jobs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 2, false)
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-25 13:21:02] local.ERROR: PHP Parse error: Syntax error, unexpected T_ENCAPSED_AND_WHITESPACE on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_ENCAPSED_AND_WHITESPACE on line 1 at C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo 'Tab...', false)
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo 'Tables: '...', true)
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo 'Tables: '...', true)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo 'Tables: '...')
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-07-25 13:21:48] local.INFO: 開始處理PDF合併任務 {"task_id":"merge_vHcKJrxykOTX3GHk_1753420363","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T05:12:43.946402Z","client_ip":"127.0.0.1"}} 
[2025-07-25 13:21:48] local.ERROR: 考生PDF合併失敗 {"applicant_id":1,"error":"沒有找到有效的PDF檔案"} 
[2025-07-25 13:21:48] local.ERROR: PDF合併任務失敗 {"task_id":"merge_vHcKJrxykOTX3GHk_1753420363","error":"沒有成功合併任何PDF檔案","trace":"#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Jobs\\ProcessPdfMergeJob->handle()
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(132): Illuminate\\Container\\Container->call(Array)
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php(136): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(125): Illuminate\\Bus\\Dispatcher->dispatchNow(Object(App\\Jobs\\ProcessPdfMergeJob), false)
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(App\\Jobs\\ProcessPdfMergeJob))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(120): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php(68): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(App\\Jobs\\ProcessPdfMergeJob))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Array)
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(444): Illuminate\\Queue\\Jobs\\Job->fire()
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(394): Illuminate\\Queue\\Worker->process('database', Object(Illuminate\\Queue\\Jobs\\DatabaseJob), Object(Illuminate\\Queue\\WorkerOptions))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(337): Illuminate\\Queue\\Worker->runJob(Object(Illuminate\\Queue\\Jobs\\DatabaseJob), 'database', Object(Illuminate\\Queue\\WorkerOptions))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}"} 
[2025-07-25 13:26:34] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 13:26:58] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 13:26:58] local.INFO: PDF合併任務已啟動 {"task_id":"merge_mAdF58aaAtepR2mu_1753421218","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T05:26:58.743291Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 13:27:01] local.INFO: 開始處理PDF合併任務 {"task_id":"merge_mAdF58aaAtepR2mu_1753421218","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T05:26:58.743291Z","client_ip":"127.0.0.1"}} 
[2025-07-25 13:27:02] local.INFO: 考生PDF合併完成 {"applicant_id":1,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/applicant_1_merged_688315a6106f4.pdf","file_count":2,"bookmark_count":2} 
[2025-07-25 13:27:02] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/applicant_1_merged_688315a6106f4.pdf","zip_name":"applicant_1_merged.pdf"} 
[2025-07-25 13:27:02] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-25 13:27:02] local.INFO: ZIP檔案創建成功 {"zip_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_13-27-02_merge_mAdF58aaAtepR2mu_1753421218.zip","added_files":1,"total_files":1,"task_id":"merge_mAdF58aaAtepR2mu_1753421218"} 
[2025-07-25 13:27:02] local.INFO: PDF合併任務完成 {"task_id":"merge_mAdF58aaAtepR2mu_1753421218","merged_files":1,"zip_file":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_13-27-02_merge_mAdF58aaAtepR2mu_1753421218.zip"} 
[2025-07-25 13:27:02] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:57:10] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/start-merge","method":"POST"} 
[2025-07-25 13:57:10] local.INFO: PDF合併任務已啟動 {"task_id":"merge_NVgz3QWkT5A7j0rl_1753423030","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T05:57:10.095464Z","client_ip":"127.0.0.1"},"client_ip":"127.0.0.1"} 
[2025-07-25 13:57:11] local.INFO: 開始處理PDF合併任務 {"task_id":"merge_NVgz3QWkT5A7j0rl_1753423030","parameters":{"exam_id":"2","exam_year":114,"requested_at":"2025-07-25T05:57:10.095464Z","client_ip":"127.0.0.1"}} 
[2025-07-25 13:57:11] local.INFO: 嘗試使用FPDI合併 {"method":"fpdi","file_count":2} 
[2025-07-25 13:57:11] local.ERROR: FPDI PDF合併失敗 {"error":"FPDI TCPDF類別不存在","file_count":2} 
[2025-07-25 13:57:11] local.WARNING: 使用FPDI合併失敗 {"method":"fpdi","error":"FPDI TCPDF類別不存在"} 
[2025-07-25 13:57:11] local.INFO: 嘗試使用TCPDF合併 {"method":"tcpdf","file_count":2} 
[2025-07-25 13:57:11] local.INFO: TCPDF PDF合併完成 {"applicant_id":1,"autono":1,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","file_count":2,"bookmark_count":2} 
[2025-07-25 13:57:11] local.INFO: 考生PDF合併完成 {"applicant_id":1,"merged_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","file_count":2} 
[2025-07-25 13:57:11] local.DEBUG: 檔案已添加到ZIP {"source_path":"C:\\Users\\<USER>\\Desktop\\rec-letter\\storage\\app/temp/merged/1.pdf","zip_name":"348.pdf"} 
[2025-07-25 13:57:11] local.DEBUG: 說明檔案已添加到ZIP  
[2025-07-25 13:57:11] local.INFO: ZIP檔案創建成功 {"zip_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_13-57-11_merge_NVgz3QWkT5A7j0rl_1753423030.zip","added_files":1,"total_files":1,"task_id":"merge_NVgz3QWkT5A7j0rl_1753423030"} 
[2025-07-25 13:57:11] local.INFO: PDF合併任務完成 {"task_id":"merge_NVgz3QWkT5A7j0rl_1753423030","merged_files":1,"zip_file":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_13-57-11_merge_NVgz3QWkT5A7j0rl_1753423030.zip"} 
[2025-07-25 13:57:11] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Laravel-RecommendationSystem","has_api_key":false,"endpoint":"api/pdf-merge/merge-status","method":"GET"} 
[2025-07-25 13:57:12] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 13:57:12] local.INFO: PDF合併檔案下載 {"task_id":"merge_NVgz3QWkT5A7j0rl_1753423030","file_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_13-57-11_merge_NVgz3QWkT5A7j0rl_1753423030.zip","client_ip":"127.0.0.1"} 
[2025-07-25 13:57:12] local.ERROR: App\Http\Controllers\Api\PdfMergeApiController::downloadMergedFile(): Return value must be of type Illuminate\Http\Response|Illuminate\Http\JsonResponse, Symfony\Component\HttpFoundation\BinaryFileResponse returned {"exception":"[object] (TypeError(code: 0): App\\Http\\Controllers\\Api\\PdfMergeApiController::downloadMergedFile(): Return value must be of type Illuminate\\Http\\Response|Illuminate\\Http\\JsonResponse, Symfony\\Component\\HttpFoundation\\BinaryFileResponse returned at C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Controllers\\Api\\PdfMergeApiController.php:270)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\PdfMergeApiController->downloadMergedFile(Object(Illuminate\\Http\\Request))
#1 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\PdfMergeApiController), 'downloadMergedF...')
#2 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#3 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#4 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#5 C:\\Users\\<USER>\\Desktop\\rec-letter\\app\\Http\\Middleware\\ApiWhitelistMiddleware.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ApiWhitelistMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#13 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\rec-letter\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\rec-letter\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#39 C:\\Program Files\\Herd\\resources\\app.asar.unpacked\\resources\\valet\\server.php(139): require('C:\\\\Users\\\\<USER>\\\\D...')
#40 {main}
"} 
[2025-07-25 13:58:53] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"GuzzleHttp/7","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 13:58:53] local.INFO: PDF合併檔案下載 {"task_id":"merge_NVgz3QWkT5A7j0rl_1753423030","file_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_13-57-11_merge_NVgz3QWkT5A7j0rl_1753423030.zip","client_ip":"127.0.0.1"} 
[2025-07-25 14:01:08] local.INFO: 使用者登出 {"user_type":"admin","ip":"127.0.0.1"} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"2","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2025-07-01T01:00:00.000000Z","end_time":"2025-08-30T09:00:00.000000Z","is_in_period":true} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"K","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2025-06-05T02:00:00.000000Z","end_time":"2025-06-10T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"E","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2025-04-28T01:00:00.000000Z","end_time":"2025-05-09T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"J","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2025-04-21T01:00:00.000000Z","end_time":"2025-05-02T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"7","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2025-04-08T02:00:00.000000Z","end_time":"2025-05-01T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"1","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2025-03-18T01:00:00.000000Z","end_time":"2025-04-08T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"P","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2025-02-18T01:00:00.000000Z","end_time":"2025-03-25T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"3","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2025-01-03T01:00:00.000000Z","end_time":"2025-01-22T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"B","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2024-12-03T01:00:00.000000Z","end_time":"2025-02-27T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:01:20] local.INFO: 招生期間檢查 {"exam_id":"I","check_time":"2025-07-25T06:01:20.362628Z","start_time":"2024-10-25T01:00:00.000000Z","end_time":"2024-11-11T09:00:00.000000Z","is_in_period":false} 
[2025-07-25 14:13:58] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
[2025-07-25 14:13:58] local.INFO: PDF合併檔案下載 {"task_id":"merge_NVgz3QWkT5A7j0rl_1753423030","file_path":"pdf_merges/merged_recommendations_exam_2_year_114_2025-07-25_13-57-11_merge_NVgz3QWkT5A7j0rl_1753423030.zip","client_ip":"127.0.0.1"} 
[2025-07-25 14:14:11] local.INFO: API訪問嘗試 {"ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","has_api_key":false,"endpoint":"api/pdf-merge/download","method":"GET"} 
