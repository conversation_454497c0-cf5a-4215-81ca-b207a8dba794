#!/bin/bash

# PDF 壓縮系統測試腳本

echo "=== PDF 壓縮系統測試腳本 ==="

# 設定變數
BASE_URL="${1:-https://rec-letter.test}"
API_KEY="${2:-your-api-key}"
EXAM_ID="${3:-2}"
EXAM_YEAR="${4:-114}"

echo "測試設定:"
echo "  基礎 URL: $BASE_URL"
echo "  API 密鑰: $API_KEY"
echo "  招生代碼: $EXAM_ID"
echo "  招生年度: $EXAM_YEAR"
echo ""

# 檢查必要工具
command -v curl >/dev/null 2>&1 || { echo "錯誤: 需要安裝 curl"; exit 1; }
command -v jq >/dev/null 2>&1 || { echo "警告: 建議安裝 jq 以便更好地處理 JSON"; }

# 步驟 1: 啟動壓縮任務
echo "步驟 1: 啟動壓縮任務..."
START_RESPONSE=$(curl -s -X POST "$BASE_URL/api/pdf-merge/start-merge" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: $API_KEY" \
  -d "{\"exam_id\": \"$EXAM_ID\", \"exam_year\": $EXAM_YEAR}")

echo "啟動回應: $START_RESPONSE"

# 檢查是否有 jq 來解析 JSON
if command -v jq >/dev/null 2>&1; then
    TASK_ID=$(echo "$START_RESPONSE" | jq -r '.data.task_id // empty')
    START_STATUS=$(echo "$START_RESPONSE" | jq -r '.status // empty')
else
    # 簡單的文字解析
    TASK_ID=$(echo "$START_RESPONSE" | grep -o '"task_id":"[^"]*"' | cut -d'"' -f4)
    START_STATUS=$(echo "$START_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
fi

if [ -z "$TASK_ID" ] || [ "$START_STATUS" != "success" ]; then
    echo "錯誤: 無法啟動壓縮任務"
    echo "回應: $START_RESPONSE"
    exit 1
fi

echo "✓ 任務已啟動，任務 ID: $TASK_ID"
echo ""

# 步驟 2: 輪詢任務狀態
echo "步驟 2: 等待任務完成..."
MAX_WAIT=300  # 最多等待 5 分鐘
WAIT_TIME=0

while [ $WAIT_TIME -lt $MAX_WAIT ]; do
    STATUS_RESPONSE=$(curl -s "$BASE_URL/api/pdf-merge/merge-status?task_id=$TASK_ID" \
      -H "X-API-Key: $API_KEY")
    
    if command -v jq >/dev/null 2>&1; then
        CURRENT_STATUS=$(echo "$STATUS_RESPONSE" | jq -r '.data.status // empty')
        PROGRESS=$(echo "$STATUS_RESPONSE" | jq -r '.data.progress // 0')
    else
        CURRENT_STATUS=$(echo "$STATUS_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        PROGRESS=$(echo "$STATUS_RESPONSE" | grep -o '"progress":[0-9]*' | cut -d':' -f2)
    fi
    
    echo "任務狀態: $CURRENT_STATUS, 進度: $PROGRESS%"
    
    if [ "$CURRENT_STATUS" = "ready" ]; then
        echo "✓ 任務完成！"
        break
    elif [ "$CURRENT_STATUS" = "failed" ]; then
        echo "✗ 任務失敗"
        echo "狀態回應: $STATUS_RESPONSE"
        exit 1
    fi
    
    sleep 5
    WAIT_TIME=$((WAIT_TIME + 5))
done

if [ $WAIT_TIME -ge $MAX_WAIT ]; then
    echo "✗ 任務超時"
    exit 1
fi

echo ""

# 步驟 3: 測試下載
echo "步驟 3: 測試下載..."

# 3a. 測試 API 下載（需要認證）
echo "3a. 測試 API 下載（需要認證）..."
API_DOWNLOAD_URL="$BASE_URL/api/pdf-merge/download?task_id=$TASK_ID"
curl -s -I -H "X-API-Key: $API_KEY" "$API_DOWNLOAD_URL" | head -1

# 3b. 測試公共下載（無需認證）
echo "3b. 測試公共下載（無需認證）..."
PUBLIC_DOWNLOAD_URL="$BASE_URL/api/public/pdf-download/$TASK_ID"
curl -s -I "$PUBLIC_DOWNLOAD_URL" | head -1

# 3c. 實際下載檔案
echo "3c. 下載檔案到本地..."
OUTPUT_FILE="test_download_$(date +%Y%m%d_%H%M%S).zip"

if curl -L "$PUBLIC_DOWNLOAD_URL" -o "$OUTPUT_FILE" 2>/dev/null; then
    if [ -f "$OUTPUT_FILE" ] && [ -s "$OUTPUT_FILE" ]; then
        FILE_SIZE=$(stat -f%z "$OUTPUT_FILE" 2>/dev/null || stat -c%s "$OUTPUT_FILE" 2>/dev/null || echo "unknown")
        echo "✓ 檔案下載成功: $OUTPUT_FILE (大小: $FILE_SIZE bytes)"
        
        # 檢查是否為有效的 ZIP 檔案
        if command -v unzip >/dev/null 2>&1; then
            if unzip -t "$OUTPUT_FILE" >/dev/null 2>&1; then
                echo "✓ ZIP 檔案格式有效"
                echo "ZIP 檔案內容:"
                unzip -l "$OUTPUT_FILE"
            else
                echo "✗ ZIP 檔案格式無效"
            fi
        fi
    else
        echo "✗ 下載的檔案為空或不存在"
    fi
else
    echo "✗ 檔案下載失敗"
fi

echo ""

# 步驟 4: 顯示測試指令
echo "步驟 4: 手動測試指令"
echo "您可以使用以下指令手動測試下載:"
echo ""
echo "wget 下載:"
echo "wget '$PUBLIC_DOWNLOAD_URL' -O manual_download.zip"
echo ""
echo "curl 下載:"
echo "curl -L '$PUBLIC_DOWNLOAD_URL' -o manual_download.zip"
echo ""
echo "API 下載 (需要認證):"
echo "curl -H 'X-API-Key: $API_KEY' '$API_DOWNLOAD_URL' -o api_download.zip"

echo ""
echo "=== 測試完成 ==="

# 清理（可選）
read -p "是否要刪除下載的測試檔案? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f "$OUTPUT_FILE"
    echo "測試檔案已刪除"
fi
