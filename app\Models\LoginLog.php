<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Request;

/**
 * 登入日誌模型
 *
 * 記錄用戶登入活動
 */
class LoginLog extends Model
{
    use HasFactory;

    /**
     * 可批量賦值的屬性
     */
    protected $fillable = [
        'user_id',
        'ip_address',
        'user_agent',
        'success',
        'failure_reason',
        'login_at',
    ];

    /**
     * 屬性類型轉換
     */
    protected $casts = [
        'success' => 'boolean',
        'login_at' => 'datetime',
    ];

    /**
     * 關聯用戶
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 記錄登入成功
     */
    public static function logSuccess(int $userId): LoginLog
    {
        return self::create([
            'user_id' => $userId,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'success' => true,
            'login_at' => now(),
        ]);
    }

    /**
     * 記錄登入失敗
     */
    public static function logFailure(?int $userId, string $reason): LoginLog
    {
        return self::create([
            'user_id' => $userId,
            'ip_address' => Request::ip(),
            'user_agent' => Request::userAgent(),
            'success' => false,
            'failure_reason' => $reason,
            'login_at' => now(),
        ]);
    }

    /**
     * 範圍查詢：成功的登入
     */
    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    /**
     * 範圍查詢：失敗的登入
     */
    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    /**
     * 範圍查詢：最近的登入
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('login_at', '>=', now()->subDays($days));
    }

    /**
     * 範圍查詢：按用戶
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 範圍查詢：按IP地址
     */
    public function scopeFromIp($query, string $ip)
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * 獲取統計數據
     */
    public static function getStats(): array
    {
        return [
            'total' => self::count(),
            'successful' => self::successful()->count(),
            'failed' => self::failed()->count(),
            'recent' => self::recent()->count(),
            'today' => self::whereDate('login_at', today())->count(),
        ];
    }

    /**
     * 獲取最近登入的用戶
     */
    public static function getRecentUsers(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return self::with('user')
            ->successful()
            ->orderBy('login_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * 獲取可疑登入活動
     */
    public static function getSuspiciousActivity(): \Illuminate\Database\Eloquent\Collection
    {
        return self::failed()
            ->selectRaw('ip_address, COUNT(*) as attempts')
            ->where('login_at', '>=', now()->subHour())
            ->groupBy('ip_address')
            ->having('attempts', '>=', 5)
            ->get();
    }

    /**
     * 清理舊的登入日誌
     */
    public static function cleanup(int $days = 90): int
    {
        return self::where('login_at', '<', now()->subDays($days))->delete();
    }
}
