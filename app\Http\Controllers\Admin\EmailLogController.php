<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EmailLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

/**
 * Email 日誌控制器
 * 
 * 管理系統的 Email 發送記錄
 */
class EmailLogController extends Controller
{
    /**
     * 顯示 Email 日誌列表
     *
     * @param Request $request
     * @return \Inertia\Response
     */
    public function index(Request $request)
    {
        try {
            $emailLogs = EmailLog::with('recommendationLetter:id,department_name,program_type')
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return Inertia::render('admin/email-logs', [
                'emailLogs' => $emailLogs,
            ]);
        } catch (\Exception $e) {
            Log::error('Email logs index error: ' . $e->getMessage());

            return back()->withErrors([
                'system' => '載入 Email 日誌失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 顯示特定 Email 日誌詳情
     *
     * @param int $id
     * @return \Inertia\Response
     */
    public function show(int $id)
    {
        try {
            $emailLog = EmailLog::with('recommendationLetter')
                ->findOrFail($id);

            return Inertia::render('admin/email-log-detail', [
                'emailLog' => $emailLog,
            ]);
        } catch (\Exception $e) {
            Log::error('Email log detail error: ' . $e->getMessage());

            return back()->withErrors([
                'system' => '載入 Email 日誌詳情失敗，請稍後再試'
            ]);
        }
    }

    /**
     * 清理舊的 Email 日誌
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cleanup(Request $request)
    {
        try {
            $days = $request->input('days', 30);

            $deletedCount = EmailLog::where('created_at', '<', now()->subDays($days))
                ->delete();

            Log::info('Email logs cleanup completed', [
                'days' => $days,
                'deleted_count' => $deletedCount
            ]);

            return response()->json([
                'success' => true,
                'message' => "已清理 {$deletedCount} 筆舊的 Email 日誌",
                'deleted_count' => $deletedCount
            ]);
        } catch (\Exception $e) {
            Log::error('Email logs cleanup error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => '清理失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 匯出 Email 日誌
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        try {
            $query = EmailLog::with('recommendationLetter:id,department_name,program_type')
                ->orderBy('created_at', 'desc');

            // 應用篩選條件
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('type')) {
                $query->where('email_type', $request->type);
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('recipient_email', 'like', "%{$search}%")
                        ->orWhere('subject', 'like', "%{$search}%");
                });
            }

            $emailLogs = $query->get();

            $filename = "email_logs_" . now()->format('Y-m-d_H-i-s') . ".csv";

            return response()->streamDownload(function () use ($emailLogs) {
                $handle = fopen('php://output', 'w');

                // CSV 標題
                fputcsv($handle, [
                    '發送時間',
                    '收件人',
                    '主旨',
                    'Email 類型',
                    '狀態',
                    '系所',
                    '學程',
                    '錯誤訊息'
                ]);

                // 資料行
                foreach ($emailLogs as $log) {
                    fputcsv($handle, [
                        $log->sent_at ?: $log->created_at,
                        $log->recipient_email,
                        $log->subject,
                        $log->email_type,
                        $log->status,
                        $log->recommendationLetter->department_name ?? 'N/A',
                        $log->recommendationLetter->program_type ?? 'N/A',
                        $log->error_message ?: ''
                    ]);
                }

                fclose($handle);
            }, $filename, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ]);
        } catch (\Exception $e) {
            Log::error('Email logs export error: ' . $e->getMessage());

            return back()->withErrors([
                'system' => '匯出失敗，請稍後再試'
            ]);
        }
    }
}
