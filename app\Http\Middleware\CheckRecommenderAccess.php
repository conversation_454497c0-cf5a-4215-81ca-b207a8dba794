<?php

namespace App\Http\Middleware;

use App\Models\Recommender;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class CheckRecommenderAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        Log::info('【Middleware】推薦人存取檢查中間件觸發', ['user' => $user?->id]);
        // Check if user is authenticated and has recommender role
        if (!$user || $user->role !== 'recommender') {
            Log::warning('【Middleware】推薦人存取檢查失敗：使用者或角色不符', ['user' => $user]);
            return redirect()->route('home')->with('error', '您沒有權限訪問此頁面。');
        }

        // Check if recommender session exists
        $recommenderId = Session::get('recommender_id');
        if (!$recommenderId) {
            Log::warning('【Middleware】推薦人會話不存在或已過期', ['user' => $user->id]);
            return redirect()->route('home')->with('error', '推薦人會話已過期，請重新登入。');
        }

        // Verify recommender exists and matches user email
        $recommender = Recommender::find($recommenderId);
        if (!$recommender || $recommender->email !== $user->email) {
            Log::warning('【Middleware】推薦人存取檢查失敗：推薦人不存在或電子郵件不匹配', [
                'session_id' => $recommenderId,
                'user_email' => $user->email,
            ]);
            Session::forget(['recommender_id', 'login_token']);
            Auth::logout();
            return redirect()->route('home')->with('error', '推薦人驗證失敗，請重新登入。');
        }

        return $next($request);
    }
}
