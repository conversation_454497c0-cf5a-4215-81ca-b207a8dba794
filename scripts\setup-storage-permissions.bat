@echo off
echo === PDF 壓縮系統存儲權限設置 ===

REM 獲取項目根目錄
set "PROJECT_ROOT=%~dp0.."
echo 項目根目錄: %PROJECT_ROOT%

echo 設置存儲目錄...

REM 確保目錄存在
if not exist "%PROJECT_ROOT%\storage\app\public\pdf_merges" mkdir "%PROJECT_ROOT%\storage\app\public\pdf_merges"
if not exist "%PROJECT_ROOT%\storage\app\private\recommendations" mkdir "%PROJECT_ROOT%\storage\app\private\recommendations"
if not exist "%PROJECT_ROOT%\storage\logs" mkdir "%PROJECT_ROOT%\storage\logs"
if not exist "%PROJECT_ROOT%\storage\framework\cache" mkdir "%PROJECT_ROOT%\storage\framework\cache"
if not exist "%PROJECT_ROOT%\storage\framework\sessions" mkdir "%PROJECT_ROOT%\storage\framework\sessions"
if not exist "%PROJECT_ROOT%\storage\framework\views" mkdir "%PROJECT_ROOT%\storage\framework\views"

echo 目錄創建完成

REM 檢查 storage link
echo 檢查 storage link...
if not exist "%PROJECT_ROOT%\public\storage" (
    echo 創建 storage link...
    cd /d "%PROJECT_ROOT%"
    php artisan storage:link
) else (
    echo storage link 已存在
)

echo.
echo === 存儲目錄結構 ===
dir "%PROJECT_ROOT%\storage\app" /AD

echo.
echo === 設置完成 ===
echo ✓ 存儲目錄已創建
echo ✓ PDF 合併目錄: storage\app\public\pdf_merges (公共訪問)
echo ✓ 推薦函目錄: storage\app\private\recommendations (私有訪問)
echo ✓ Storage link 已創建

echo.
echo 測試建議：
echo 1. 執行測試指令: php artisan pdf:test-compression --list-available
echo 2. 檢查 Web 服務器是否可以訪問 storage\app\public 目錄
echo 3. 確認防火牆設置允許外部訪問

pause
