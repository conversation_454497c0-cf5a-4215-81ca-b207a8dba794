<?php

namespace App\Http\Middleware;

use App\Models\User;
use App\Models\UserAgreement;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class CheckUserAgreement
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        /** @var User|null $user */
        $user = Auth::user();

        if (!$user) {
            return $next($request);
        }

        // 管理員不需要檢查使用者協議
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Skip agreement check for certain routes
        $skipRoutes = [
            'user-agreement.show',
            'user-agreement.store',
            'logout',
            'auth.*',
        ];

        foreach ($skipRoutes as $pattern) {
            if ($request->routeIs($pattern)) {
                return $next($request);
            }
        }

        // Log::info('【Middleware】CheckUserAgreement triggered', ['user' => $user?->id]);

        // Check if user has agreed to terms
        if (!UserAgreement::hasAgreed($user)) {
            return redirect()->route('user-agreement.show');
        }

        return $next($request);
    }
}
