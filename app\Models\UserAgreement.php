<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class UserAgreement extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'user_type',
        'agree_status',
    ];

    protected $casts = [
        'agree_status' => 'boolean',
    ];

    /**
     * Get the user that owns the agreement.
     */
    public function user(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Check if a user has agreed to the terms.
     */
    public static function hasAgreed($user): bool
    {
        return self::where('user_id', $user->id)
            ->where('user_type', get_class($user))
            ->where('agree_status', true)
            ->exists();
    }

    /**
     * Create or update agreement for a user.
     */
    public static function setAgreement($user, bool $status = true): self
    {
        return self::updateOrCreate(
            [
                'user_id' => $user->id,
                'user_type' => get_class($user),
            ],
            [
                'agree_status' => $status,
            ]
        );
    }
}
