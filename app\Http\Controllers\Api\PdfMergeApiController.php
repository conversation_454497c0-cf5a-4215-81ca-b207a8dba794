<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\ProcessPdfMergeJob;
use App\Models\PdfMergeTask;
use App\Models\RecommendationLetter;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

/**
 * PDF合併API控制器
 * 
 * 提供外部系統調用的PDF合併功能API
 */
class PdfMergeApiController extends Controller
{
    /**
     * 啟動PDF合併任務
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function startMerge(Request $request): JsonResponse
    {
        try {
            // 驗證請求參數
            $validator = Validator::make($request->all(), [
                'exam_id' => 'required|string|max:50',
                'exam_year' => 'required|integer|min:100|max:200',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            // 準備合併參數
            $parameters = array_filter([
                'exam_id' => $request->input('exam_id'),
                'exam_year' => $request->input('exam_year'),
                'requested_at' => now()->toISOString(),
                'client_ip' => $request->ip(),
            ]);

            // 檢查是否有符合條件的推薦函
            $availableRecommendations = $this->checkAvailableRecommendations($parameters);

            if ($availableRecommendations['total_count'] === 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => '沒有找到符合條件的已提交推薦函',
                    'details' => [
                        'exam_id' => $parameters['exam_id'],
                        'exam_year' => $parameters['exam_year'],
                        'total_recommendations' => 0,
                        'applicants_with_recommendations' => 0
                    ]
                ], 404);
            }

            // 創建合併任務
            $task = PdfMergeTask::createTask($parameters);

            // 設定預期的檔案數量
            $task->update([
                'total_files' => $availableRecommendations['applicant_count']
            ]);

            // 派發背景任務
            ProcessPdfMergeJob::dispatch($task->task_id, $parameters);

            Log::info('PDF合併任務已啟動', [
                'task_id' => $task->task_id,
                'parameters' => $parameters,
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'processing',
                'task_id' => $task->task_id,
                'message' => '合併任務已啟動，請使用task_id查詢進度'
            ]);
        } catch (\Exception $e) {
            Log::error('啟動PDF合併任務失敗', [
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '啟動合併任務失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 查詢合併任務狀態
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getMergeStatus(Request $request): JsonResponse
    {
        Log::info('📥 收到任務狀態查詢請求', [
            'query' => $request->query(),
            'input' => $request->all(),
            'ip' => $request->ip(),
        ]);

        if (!$request->has('task_id')) {
            Log::warning('⚠️ 任務狀態查詢請求未提供 task_id', [
                'query' => $request->query(),
                'ip' => $request->ip(),
            ]);
        }

        try {

            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $taskId = $request->input('task_id');
            $task = PdfMergeTask::findByTaskId($taskId);

            if (!$task) {
                Log::warning('🔍 查無任務', [
                    'task_id' => $taskId,
                    'ip' => $request->ip(),
                ]);

                return response()->json([
                    'status' => 'error',
                    'message' => '找不到指定的任務'
                ], 404);
            }


            // 檢查任務是否過期
            if ($task->isExpired()) {
                $task->update(['status' => PdfMergeTask::STATUS_EXPIRED]);

                return response()->json([
                    'status' => 'expired',
                    'message' => '任務已過期',
                    'task_id' => $taskId
                ]);
            }

            // 根據任務狀態返回不同的響應
            $response = [
                'status' => $task->status,
                'task_id' => $taskId,
                'progress' => $task->progress,
                'created_at' => $task->created_at->toISOString(),
            ];

            switch ($task->status) {
                case PdfMergeTask::STATUS_PROCESSING:
                    $response['message'] = '任務處理中';
                    $response['processed_files'] = $task->processed_files;
                    $response['total_files'] = $task->total_files;
                    break;

                case PdfMergeTask::STATUS_READY:
                    if ($task->isReady()) {
                        $response['message'] = '任務完成，可以下載';
                        $response['download_url'] = $task->download_url;
                        $response['expires_at'] = $task->expires_at?->toISOString();
                    } else {
                        $response['status'] = 'error';
                        $response['message'] = '檔案不存在或已損壞';
                    }
                    break;

                case PdfMergeTask::STATUS_FAILED:
                    $response['message'] = '任務處理失敗';
                    $response['error'] = $task->error_message;
                    break;

                case PdfMergeTask::STATUS_EXPIRED:
                    $response['message'] = '任務已過期';
                    break;

                default:
                    $response['message'] = '未知狀態';
                    break;
            }

            Log::info('✅ 回傳任務狀態', [
                'task_id' => $taskId,
                'status' => $response['status'],
                'ip' => $request->ip(),
            ]);

            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('查詢PDF合併任務狀態失敗', [
                'error' => $e->getMessage(),
                'task_id' => $request->input('task_id'),
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '查詢任務狀態失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 下載合併後的PDF檔案
     * 
     * @param Request $request
     * @return Response|JsonResponse
     */
    public function downloadMergedFile(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'task_id' => 'required|string',
                'token' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '參數驗證失敗',
                    'errors' => $validator->errors()
                ], 422);
            }

            $taskId = $request->input('task_id');
            $token = $request->input('token');

            // 驗證token
            $expectedToken = hash('sha256', $taskId . config('app.key'));
            if (!hash_equals($expectedToken, $token)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '無效的下載token'
                ], 403);
            }

            $task = PdfMergeTask::findByTaskId($taskId);

            if (!$task) {
                return response()->json([
                    'status' => 'error',
                    'message' => '找不到指定的任務'
                ], 404);
            }

            if (!$task->isReady()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '檔案尚未準備好或已過期'
                ], 400);
            }

            // 使用 public 磁碟來獲取文件
            $disk = Storage::disk('public');

            if (!$disk->exists($task->zip_file_path)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '檔案不存在',
                    'debug' => [
                        'zip_file_path' => $task->zip_file_path,
                        'disk_path' => $disk->path($task->zip_file_path)
                    ]
                ], 404);
            }

            $filePath = $disk->path($task->zip_file_path);

            Log::info('PDF壓縮檔案下載', [
                'task_id' => $taskId,
                'file_path' => $task->zip_file_path,
                'full_path' => $filePath,
                'file_size' => filesize($filePath),
                'client_ip' => $request->ip()
            ]);

            return response()->download($filePath, basename($task->zip_file_path), [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . basename($task->zip_file_path) . '"'
            ]);
        } catch (\Exception $e) {
            Log::error('下載PDF壓縮檔案失敗', [
                'error' => $e->getMessage(),
                'task_id' => $request->input('task_id'),
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '下載檔案失敗，請稍後再試'
            ], 500);
        }
    }

    /**
     * 公共下載方法（無需認證）
     *
     * @param string $taskId
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function publicDownload(string $taskId, Request $request)
    {
        try {
            $task = PdfMergeTask::findByTaskId($taskId);

            if (!$task) {
                return response()->json([
                    'status' => 'error',
                    'message' => '找不到指定的任務'
                ], 404);
            }

            if (!$task->isReady()) {
                return response()->json([
                    'status' => 'error',
                    'message' => '檔案尚未準備好或已過期'
                ], 400);
            }

            // 使用 public 磁碟來獲取文件
            $disk = Storage::disk('public');

            if (!$disk->exists($task->zip_file_path)) {
                return response()->json([
                    'status' => 'error',
                    'message' => '檔案不存在'
                ], 404);
            }

            $filePath = $disk->path($task->zip_file_path);

            Log::info('公共PDF壓縮檔案下載', [
                'task_id' => $taskId,
                'file_path' => $task->zip_file_path,
                'client_ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            return response()->download($filePath, basename($task->zip_file_path), [
                'Content-Type' => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . basename($task->zip_file_path) . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);
        } catch (\Exception $e) {
            Log::error('公共下載PDF壓縮檔案失敗', [
                'error' => $e->getMessage(),
                'task_id' => $taskId,
                'client_ip' => $request->ip()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '下載失敗'
            ], 500);
        }
    }

    /**
     * 清理過期任務（內部API）
     * 
     * @return JsonResponse
     */
    public function cleanupExpiredTasks(): JsonResponse
    {
        try {
            $cleanedCount = PdfMergeTask::cleanupExpiredTasks();

            Log::info('PDF合併任務清理完成', [
                'cleaned_count' => $cleanedCount
            ]);

            return response()->json([
                'status' => 'success',
                'message' => "已清理 {$cleanedCount} 個過期任務",
                'cleaned_count' => $cleanedCount
            ]);
        } catch (\Exception $e) {
            Log::error('清理過期任務失敗', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => '清理過期任務失敗'
            ], 500);
        }
    }

    /**
     * 檢查可用的推薦函數量
     *
     * @param array $parameters
     * @return array
     */
    protected function checkAvailableRecommendations(array $parameters): array
    {
        $query = RecommendationLetter::where('status', RecommendationLetter::STATUS_SUBMITTED)
            ->whereNotNull('pdf_path');

        // 根據參數過濾
        if (isset($parameters['exam_id'])) {
            $query->where('exam_id', $parameters['exam_id']);
        }

        if (isset($parameters['exam_year'])) {
            $query->where('exam_year', $parameters['exam_year']);
        }

        // 獲取推薦函並按考生分組
        $recommendations = $query->with(['applicant'])->get();

        $groupedByApplicant = $recommendations->groupBy('applicant_id');

        // 檢查每個考生的PDF檔案是否存在
        $validApplicants = 0;
        $validRecommendations = 0;

        foreach ($groupedByApplicant as $applicantId => $applicantRecommendations) {
            $hasValidPdf = false;

            foreach ($applicantRecommendations as $recommendation) {
                if (Storage::disk('local')->exists($recommendation->pdf_path)) {
                    $validRecommendations++;
                    $hasValidPdf = true;
                }
            }

            if ($hasValidPdf) {
                $validApplicants++;
            }
        }

        return [
            'total_count' => $validRecommendations,
            'applicant_count' => $validApplicants,
            'total_applicants' => count($groupedByApplicant),
            'recommendations_by_applicant' => $groupedByApplicant->map(function ($recs) {
                return count($recs);
            })->toArray()
        ];
    }
}
