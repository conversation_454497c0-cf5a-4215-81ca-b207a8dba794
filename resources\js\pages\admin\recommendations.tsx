import AdminLayout from '@/layouts/admin-layout';
import { Head } from '@inertiajs/react';
import { Eye, FileText, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { useState } from 'react';
import StatusBadge from '@/components/admin/StatusBadge';
import SearchAndFilter from '@/components/admin/SearchAndFilter';
import DataTable from '@/components/admin/DataTable';
import StatsCards from '@/components/admin/StatsCards';

interface Recommendation {
    id: number;
    applicant_id: number;
    external_autono: string;
    department_name: string;
    program_type: string;
    status: string;
    recommender_name?: string;
    recommender_email?: string;
    submitted_at?: string;
    created_at: string;
    applicant?: {
        user?: {
            name: string;
            email: string;
        };
    };
}

interface RecommendationsProps {
    recommendations: Recommendation[];
}

export default function Recommendations({ recommendations }: RecommendationsProps) {
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [departmentFilter, setDepartmentFilter] = useState('all');

    // 麵包屑導航
    const breadcrumbs = [
        { title: '儀表板', href: '/dashboard' },
        { title: '推薦函管理', href: '/admin/recommendations' },
    ];

    // 過濾推薦函
    const filteredRecommendations = recommendations.filter((rec) => {
        const matchesSearch =
            rec.applicant?.user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            rec.recommender_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            rec.department_name.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' || rec.status === statusFilter;
        const matchesDepartment = departmentFilter === 'all' || rec.department_name === departmentFilter;

        return matchesSearch && matchesStatus && matchesDepartment;
    });

    // 獲取所有系所
    const departments = Array.from(new Set(recommendations.map((rec) => rec.department_name)));

    return (
        <AdminLayout breadcrumbs={breadcrumbs} title="推薦函管理" description="查看和管理所有推薦函申請">
            <Head title="推薦函管理" />

            <div className="space-y-6 p-6">
                {/* 搜尋和篩選區域 */}
                <SearchAndFilter
                    searchTerm={searchTerm}
                    onSearchChange={setSearchTerm}
                    searchPlaceholder="搜尋申請人、推薦人或系所..."
                    filters={[
                        {
                            key: 'status',
                            label: '狀態',
                            value: statusFilter,
                            onChange: setStatusFilter,
                            options: [
                                { value: 'pending', label: '待處理' },
                                { value: 'submitted', label: '已提交' },
                                { value: 'declined', label: '已婉拒' },
                                { value: 'withdrawn', label: '已撤回' },
                            ],
                        },
                        {
                            key: 'department',
                            label: '系所',
                            value: departmentFilter,
                            onChange: setDepartmentFilter,
                            options: departments.map((dept) => ({ value: dept, label: dept })),
                        },
                    ]}
                />

                {/* 統計資訊 */}
                <StatsCards
                    stats={[
                        {
                            title: '總推薦函數',
                            value: recommendations.length,
                            icon: FileText,
                            color: 'default',
                        },
                        {
                            title: '已提交',
                            value: recommendations.filter((r) => r.status === 'submitted').length,
                            icon: CheckCircle,
                            color: 'green',
                        },
                        {
                            title: '待處理',
                            value: recommendations.filter((r) => r.status === 'pending').length,
                            icon: Clock,
                            color: 'yellow',
                        },
                        {
                            title: '已婉拒',
                            value: recommendations.filter((r) => r.status === 'declined').length,
                            icon: AlertCircle,
                            color: 'red',
                        },
                    ]}
                />

                {/* 推薦函列表 */}
                <DataTable
                    data={filteredRecommendations}
                    title="推薦函列表"
                    description={`顯示 ${filteredRecommendations.length} / ${recommendations.length} 筆推薦函`}
                    emptyMessage="沒有找到符合條件的推薦函"
                    columns={[
                        {
                            key: 'applicant_name',
                            label: '申請人',
                            sortable: true,
                            render: (_, row) => (
                                <div>
                                    <p className="font-medium">{row.applicant?.user?.name || '未知申請人'}</p>
                                    <p className="text-sm text-gray-500">{row.applicant?.user?.email}</p>
                                </div>
                            ),
                        },
                        {
                            key: 'recommender_name',
                            label: '推薦人',
                            sortable: true,
                            render: (_, row) => (
                                <div>
                                    <p className="font-medium">{row.recommender_name || '未指定'}</p>
                                    <p className="text-sm text-gray-500">{row.recommender_email}</p>
                                </div>
                            ),
                        },
                        {
                            key: 'department_name',
                            label: '系所/學程',
                            sortable: true,
                            render: (_, row) => (
                                <div>
                                    <p className="font-medium">{row.department_name}</p>
                                    <p className="text-sm text-gray-500">{row.program_type}</p>
                                </div>
                            ),
                        },
                        {
                            key: 'status',
                            label: '狀態',
                            sortable: true,
                            render: (value) => <StatusBadge status={value} type="recommendation" />,
                        },
                        {
                            key: 'created_at',
                            label: '時間',
                            sortable: true,
                            render: (_, row) => (
                                <div className="text-sm">
                                    {row.submitted_at
                                        ? `提交於 ${new Date(row.submitted_at).toLocaleDateString()}`
                                        : `建立於 ${new Date(row.created_at).toLocaleDateString()}`}
                                </div>
                            ),
                        },
                    ]}
                    actions={[
                        {
                            key: 'view',
                            label: '查看',
                            icon: <Eye className="mr-1 h-4 w-4" />,
                            variant: 'outline',
                            onClick: (row) => {
                                // 這裡可以添加查看詳情的邏輯
                                console.log('查看推薦函:', row.id);
                            },
                        },
                    ]}
                    exportable={true}
                    onExport={() => {
                        // 這裡可以添加匯出邏輯
                        console.log('匯出推薦函資料');
                    }}
                />
            </div>
        </AdminLayout>
    );
}
