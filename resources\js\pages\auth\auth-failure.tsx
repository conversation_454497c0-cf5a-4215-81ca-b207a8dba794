import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, Home, RefreshCw } from 'lucide-react';
import { router } from '@inertiajs/react';

interface Props {
    message: string;
    type: 'applicant' | 'recommender';
}

export default function AuthFailure({ message, type }: Props) {
    const handleRetry = () => {
        window.location.reload();
    };

    const handleGoHome = () => {
        router.visit('/');
    };

    const getTitle = () => {
        switch (type) {
            case 'applicant':
                return '申請人登入失敗';
            case 'recommender':
                return '推薦人登入失敗';
            default:
                return '登入失敗';
        }
    };

    const getDescription = () => {
        switch (type) {
            case 'applicant':
                return '無法透過外部系統驗證您的身份';
            case 'recommender':
                return '無法驗證推薦人登入權限';
            default:
                return '身份驗證失敗';
        }
    };

    return (
        <>
            <Head title={getTitle()} />

            <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
                <div className="w-full max-w-md space-y-8">
                    <Card className="border-red-200 bg-red-50">
                        <CardHeader className="text-center">
                            <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                                <AlertCircle className="h-8 w-8 text-red-600" />
                            </div>
                            <CardTitle className="mt-4 text-xl font-semibold text-red-900">
                                {getTitle()}
                            </CardTitle>
                            <p className="text-sm text-red-700">
                                {getDescription()}
                            </p>
                        </CardHeader>

                        <CardContent className="space-y-6">
                            <div className="rounded-lg border border-red-200 bg-white p-4">
                                <h4 className="font-medium text-red-900 mb-2">錯誤詳情</h4>
                                <p className="text-sm text-red-700">{message}</p>
                            </div>

                            {type === 'applicant' && (
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                    <h4 className="font-medium text-blue-900 mb-2">申請人登入說明</h4>
                                    <ul className="text-sm text-blue-700 space-y-1">
                                        <li>• 請確認您是透過正確的連結進入系統</li>
                                        <li>• 連結可能已過期，請聯繫相關單位重新取得</li>
                                        <li>• 如持續發生問題，請聯繫系統管理員</li>
                                    </ul>
                                </div>
                            )}

                            {type === 'recommender' && (
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                                    <h4 className="font-medium text-blue-900 mb-2">推薦人登入說明</h4>
                                    <ul className="text-sm text-blue-700 space-y-1">
                                        <li>• 請確認您是透過推薦函邀請信中的連結進入</li>
                                        <li>• 連結可能已過期，請聯繫申請人重新發送邀請</li>
                                        <li>• 如持續發生問題，請聯繫申請人或系統管理員</li>
                                    </ul>
                                </div>
                            )}

                            <div className="flex flex-col gap-3 sm:flex-row">
                                <Button 
                                    onClick={handleRetry} 
                                    variant="outline" 
                                    className="flex-1"
                                >
                                    <RefreshCw className="mr-2 h-4 w-4" />
                                    重試
                                </Button>
                                <Button 
                                    onClick={handleGoHome} 
                                    className="flex-1"
                                >
                                    <Home className="mr-2 h-4 w-4" />
                                    返回首頁
                                </Button>
                            </div>

                            <div className="text-center">
                                <p className="text-xs text-gray-500">
                                    如需協助，請聯繫系統管理員
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}
