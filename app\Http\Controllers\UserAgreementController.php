<?php

namespace App\Http\Controllers;

use App\Models\UserAgreement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class UserAgreementController extends Controller
{
    /**
     * 顯示使用者協議頁面
     */
    public function show(): Response
    {
        $user = Auth::user();

        return Inertia::render('user-agreement', [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' =>  $user->phone,
            ],
            'userRole' => $user->role,
        ]);
    }

    /**
     * 保存使用者同意協議
     */
    public function store(Request $request)
    {
        $request->validate([
            'agree' => 'required|boolean',
        ]);

        $user = Auth::user();

        // 如果不同意，則登出使用者
        if (!$request->agree) {
            Auth::logout();
            return back()->withErrors([
                'agree' => '您必須同意個人資料使用條款才能繼續使用系統。'
            ]);
        }

        UserAgreement::setAgreement($user, true);

        return redirect()->route('dashboard')->with('success', '感謝您同意個人資料使用條款。');
    }
}
