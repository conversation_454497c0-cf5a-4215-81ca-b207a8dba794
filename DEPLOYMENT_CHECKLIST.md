# 推薦函系統部署檢查清單

## 上線前必須執行的安全檢查

### 1. 密鑰重新產生 🔑
- [ ] 執行 `php artisan key:generate` 重新產生應用程式密鑰
- [ ] 確認 `.env` 檔案中的 `APP_KEY` 已更新
- [ ] 備份舊的密鑰（如果需要解密舊資料）

### 2. API 密鑰更新 🔐
- [ ] 更新 `API_SECRET` 為新的安全密鑰
- [ ] 確保新密鑰長度至少 32 字元
- [ ] 與外部系統協調更新對應的密鑰
- [ ] 測試 API 連線是否正常

### 3. 環境設定檢查 ⚙️
- [ ] 確認 `APP_ENV` 設為 `production`
- [ ] 確認 `APP_DEBUG` 設為 `false`
- [ ] 確認 `APP_URL` 設為正確的生產環境網址
- [ ] 檢查資料庫連線設定

### 4. API 白名單設定 🛡️
- [ ] 更新 `API_WHITELIST_IPS` 為生產環境的 IP 白名單
- [ ] 移除開發環境的 IP（如 127.0.0.1, localhost）
- [ ] 確認只有授權的系統可以訪問 API
- [ ] 測試 API 白名單是否正常運作

### 5. 外部系統整合 🔗
- [ ] 確認 `API_BASE_URL` 指向正確的外部系統端點
- [ ] 測試與外部系統的連線
- [ ] 確認外部系統的 API 版本相容性
- [ ] 測試資料同步功能

### 6. 郵件設定 📧
- [ ] 確認外部郵件 API 設定正確
- [ ] 測試郵件發送功能
- [ ] 確認郵件模板顯示正常
- [ ] 檢查郵件日誌記錄

### 7. 檔案存儲設定 📁
- [ ] 確認 PDF 存儲路徑權限正確
- [ ] 測試 PDF 上傳和下載功能
- [ ] 檢查存儲空間是否足夠
- [ ] 設定檔案備份策略

### 8. 資料庫設定 🗄️
- [ ] 執行資料庫遷移 `php artisan migrate --force`
- [ ] 初始化系統設定 `php artisan tinker` -> `App\Models\SystemSetting::initializeDefaults()`
- [ ] 檢查資料庫索引是否建立
- [ ] 設定資料庫備份策略

### 9. 快取和效能 ⚡
- [ ] 清除所有快取 `php artisan cache:clear`
- [ ] 清除設定快取 `php artisan config:clear`
- [ ] 清除路由快取 `php artisan route:clear`
- [ ] 清除視圖快取 `php artisan view:clear`
- [ ] 建立生產環境快取 `php artisan config:cache`
- [ ] 建立路由快取 `php artisan route:cache`

### 10. 安全性檢查 🔒
- [ ] 確認所有敏感資訊都在 `.env` 檔案中
- [ ] 檢查檔案權限設定
- [ ] 確認 HTTPS 設定正確
- [ ] 檢查 CSRF 保護是否啟用
- [ ] 測試登入限制功能

### 11. 日誌和監控 📊
- [ ] 確認日誌檔案路徑和權限
- [ ] 設定日誌輪轉策略
- [ ] 測試系統日誌記錄功能
- [ ] 設定錯誤監控和通知

### 12. 功能測試 ✅
- [ ] 測試管理員登入功能
- [ ] 測試申請人登入功能
- [ ] 測試推薦人登入功能
- [ ] 測試推薦函建立流程
- [ ] 測試推薦函提交流程
- [ ] 測試郵件通知功能
- [ ] 測試 PDF 生成功能
- [ ] 測試系統設定功能
- [ ] 測試維護模式功能

## 環境變數範例

```bash
# 應用程式基本設定
APP_NAME="推薦函管理系統"
APP_ENV=production
APP_KEY=base64:YOUR_NEW_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://your-domain.com

# 資料庫設定
DB_CONNECTION=mysql
DB_HOST=your-db-host
DB_PORT=3306
DB_DATABASE=your-database
DB_USERNAME=your-username
DB_PASSWORD=your-password

# API 系統整合設定
API_BASE_URL=https://your-external-api.com/api/v1/recommendation_system
API_SECRET=YOUR_NEW_API_SECRET_HERE
API_TIMEOUT=30

# API 白名單設定（生產環境）
API_WHITELIST_IPS=your.server.ip.1,your.server.ip.2
API_WHITELIST_USER_AGENTS=Laravel-RecommendationSystem,ExternalSystem-API

# 外部郵件 API 設定
MAIL_EXTERNAL_API_ENABLED=true
MAIL_EXTERNAL_API_URL=https://your-mail-api.com/sendmail.php
MAIL_EXTERNAL_API_ACCOUNT=your-mail-account
MAIL_EXTERNAL_API_PASSWORD=your-mail-password
```

## 部署後驗證

### 1. 基本功能驗證
- [ ] 訪問首頁是否正常
- [ ] 管理員登入是否正常
- [ ] API 端點是否回應正常
- [ ] 資料庫連線是否正常

### 2. 安全性驗證
- [ ] 嘗試使用舊的 API 密鑰（應該被拒絕）
- [ ] 嘗試從未授權 IP 訪問 API（應該被拒絕）
- [ ] 檢查敏感資訊是否洩露

### 3. 效能驗證
- [ ] 頁面載入速度是否正常
- [ ] API 回應時間是否在可接受範圍
- [ ] 資料庫查詢效能是否正常

## 回滾計劃

如果部署出現問題，請按以下步驟回滾：

1. 恢復舊版本的應用程式代碼
2. 恢復舊版本的 `.env` 檔案
3. 如果有資料庫變更，恢復資料庫備份
4. 清除所有快取
5. 重新啟動 Web 服務器

## 聯絡資訊

- 系統管理員：[管理員聯絡方式]
- 技術支援：[技術支援聯絡方式]
- 緊急聯絡：[緊急聯絡方式]

---

**重要提醒：請在正式部署前在測試環境完整執行此檢查清單！**
