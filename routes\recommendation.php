<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\RecommendationController;
use App\Http\Controllers\Admin\QuestionnaireController;
use App\Http\Controllers\Auth\RecommenderAuthController;
use App\Http\Controllers\FileController;

/*
|--------------------------------------------------------------------------
| 推薦函相關操作路由
|--------------------------------------------------------------------------
|
| 這裡定義所有與推薦函系統相關的路由
|
*/

/**
 * 推薦函相關操作
 * 
 * middleware:
 * - auth: 確保使用者已登入
 * - check.user.agreement: 確保使用者已同意使用條款
 * - check.system.access: 確保使用者有權限訪問系統
 * - check.system.timing:user_exam_period: 確保使用者在對應的期間內允許訪問與操作
 */
Route::middleware(['auth', 'check.user.agreement', 'check.system.access', 'check.system.timing:user_exam_period'])->prefix('recommendations')->name('recommendations.')->group(function () {
  /**
   * 申請人操作
   * middleware:
   * - check.applicant.access: 確保登入人為考生
   */
  Route::middleware(['check.applicant.access'])->group(function () {
    Route::post('/', [RecommendationController::class, 'createRecommendation'])->name('create'); // 建立推薦函
    Route::post('/{id}/withdraw', [RecommendationController::class, 'withdraw'])->name('applicant.withdraw'); // 撤回推薦函

    Route::post('/{id}/remind', [RecommendationController::class, 'sendReminder'])->middleware('check.reminder.cooldown')->name('remind'); // 發送提醒(冷卻檢查)
  });

  //  
  /**
   * 推薦人操作
   * middleware:
   * - check.recommender.access: 確保登入人為推薦人
   */
  Route::middleware(['check.recommender.access'])->group(function () {
    Route::post('/{id}/submit', [RecommendationController::class, 'submitRecommendation'])->name('submit'); // 提交推薦函
    Route::post('/{id}/decline', [RecommendationController::class, 'declineRecommendation'])->name('decline'); // 拒絕推薦函
    Route::post('/{id}/withdraw', [RecommendationController::class, 'withdrawRecommendation'])->name('recommender.withdraw'); // 撤回推薦函

    // 這邊再包一層與操作分開
    // Route::get('/questionnaire/template/{recommendationId}', [QuestionnaireController::class, 'getTemplate'])->name('questionnaire.template'); // 取得問卷模板 todo fix
    Route::post('/profile/update', [RecommenderAuthController::class, 'updateProfile'])->name('profile.update'); // 更新個人資料
  });
});

// 檔案訪問路由 (考生以外的身分才可以訪問)
Route::middleware(['auth', 'check.user.agreement', 'role:admin,recommender'])->prefix('files')->name('files.')->group(function () {
  Route::get('/preview-pdf', [FileController::class, 'previewPdf'])->name('preview-pdf'); // 預覽推薦人提交的推薦函(瀏覽器)
});
