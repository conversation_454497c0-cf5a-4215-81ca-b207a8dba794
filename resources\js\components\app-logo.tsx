import { useLanguage } from '@/hooks/use-language';
import AppLogoIcon from './app-logo-icon';

export default function AppLogo() {
    const { t } = useLanguage();
    return (
        <>
            <div className="flex aspect-square size-8 items-center justify-center rounded-md text-sidebar-primary-foreground">
                <AppLogoIcon />
            </div>
            <div className="ml-1 grid flex-1 text-left text-sm">
                {/* 大裝置 */}
                <span className="mb-0.5 hidden truncate leading-tight font-semibold sm:inline">
                    {t('common.schoolName')} – {t('common.appName')}
                </span>

                {/* 小裝置 */}
                <span className="mb-0.5 truncate leading-tight font-semibold sm:hidden">
                    {t('common.schoolName_short')} – {t('common.appName')}
                </span>
            </div>
        </>
    );
}
