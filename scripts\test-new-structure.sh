#!/bin/bash

# 測試新的壓縮檔結構腳本

echo "=== 測試新的 PDF 壓縮檔結構 ==="

# 設定變數
BASE_URL="${1:-https://rec-letter.test}"
API_KEY="${2:-your-api-key}"
EXAM_ID="${3:-2}"
EXAM_YEAR="${4:-114}"

echo "測試設定:"
echo "  基礎 URL: $BASE_URL"
echo "  招生代碼: $EXAM_ID"
echo "  招生年度: $EXAM_YEAR"
echo ""

# 檢查必要工具
command -v curl >/dev/null 2>&1 || { echo "錯誤: 需要安裝 curl"; exit 1; }
command -v unzip >/dev/null 2>&1 || { echo "錯誤: 需要安裝 unzip"; exit 1; }

# 步驟 1: 啟動壓縮任務
echo "步驟 1: 啟動壓縮任務..."
START_RESPONSE=$(curl -s -X POST "$BASE_URL/api/pdf-merge/start-merge" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: $API_KEY" \
  -d "{\"exam_id\": \"$EXAM_ID\", \"exam_year\": $EXAM_YEAR}")

echo "啟動回應: $START_RESPONSE"

# 解析 task_id
if command -v jq >/dev/null 2>&1; then
    TASK_ID=$(echo "$START_RESPONSE" | jq -r '.data.task_id // empty')
    START_STATUS=$(echo "$START_RESPONSE" | jq -r '.status // empty')
else
    TASK_ID=$(echo "$START_RESPONSE" | grep -o '"task_id":"[^"]*"' | cut -d'"' -f4)
    START_STATUS=$(echo "$START_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
fi

if [ -z "$TASK_ID" ] || [ "$START_STATUS" != "success" ]; then
    echo "錯誤: 無法啟動壓縮任務"
    exit 1
fi

echo "✓ 任務已啟動，任務 ID: $TASK_ID"

# 步驟 2: 等待任務完成
echo ""
echo "步驟 2: 等待任務完成..."
MAX_WAIT=300
WAIT_TIME=0

while [ $WAIT_TIME -lt $MAX_WAIT ]; do
    STATUS_RESPONSE=$(curl -s "$BASE_URL/api/pdf-merge/merge-status?task_id=$TASK_ID" \
      -H "X-API-Key: $API_KEY")
    
    if command -v jq >/dev/null 2>&1; then
        CURRENT_STATUS=$(echo "$STATUS_RESPONSE" | jq -r '.data.status // empty')
        PROGRESS=$(echo "$STATUS_RESPONSE" | jq -r '.data.progress // 0')
    else
        CURRENT_STATUS=$(echo "$STATUS_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
        PROGRESS=$(echo "$STATUS_RESPONSE" | grep -o '"progress":[0-9]*' | cut -d':' -f2)
    fi
    
    echo "任務狀態: $CURRENT_STATUS, 進度: $PROGRESS%"
    
    if [ "$CURRENT_STATUS" = "ready" ]; then
        echo "✓ 任務完成！"
        break
    elif [ "$CURRENT_STATUS" = "failed" ]; then
        echo "✗ 任務失敗"
        exit 1
    fi
    
    sleep 5
    WAIT_TIME=$((WAIT_TIME + 5))
done

if [ $WAIT_TIME -ge $MAX_WAIT ]; then
    echo "✗ 任務超時"
    exit 1
fi

# 步驟 3: 下載並檢查檔案結構
echo ""
echo "步驟 3: 下載並檢查檔案結構..."

PUBLIC_DOWNLOAD_URL="$BASE_URL/api/public/pdf-download/$TASK_ID"
OUTPUT_FILE="test_structure_$(date +%Y%m%d_%H%M%S).zip"

echo "下載 URL: $PUBLIC_DOWNLOAD_URL"

if curl -L "$PUBLIC_DOWNLOAD_URL" -o "$OUTPUT_FILE" 2>/dev/null; then
    if [ -f "$OUTPUT_FILE" ] && [ -s "$OUTPUT_FILE" ]; then
        FILE_SIZE=$(stat -f%z "$OUTPUT_FILE" 2>/dev/null || stat -c%s "$OUTPUT_FILE" 2>/dev/null || echo "unknown")
        echo "✓ 檔案下載成功: $OUTPUT_FILE (大小: $FILE_SIZE bytes)"
        
        # 檢查檔案名稱格式
        EXPECTED_PREFIX="recommendations_${EXAM_ID}_${EXAM_YEAR}_"
        if [[ "$OUTPUT_FILE" == *"$EXPECTED_PREFIX"* ]] || echo "$OUTPUT_FILE" | grep -q "test_structure"; then
            echo "✓ 檔案名稱格式正確"
        else
            echo "⚠️  檔案名稱格式可能不符合預期"
        fi
        
        # 檢查 ZIP 檔案內容
        echo ""
        echo "檢查 ZIP 檔案內容..."
        if unzip -t "$OUTPUT_FILE" >/dev/null 2>&1; then
            echo "✓ ZIP 檔案格式有效"
            
            echo ""
            echo "ZIP 檔案結構:"
            unzip -l "$OUTPUT_FILE"
            
            echo ""
            echo "詳細目錄結構分析:"
            
            # 創建臨時目錄解壓縮
            TEMP_DIR="temp_extract_$(date +%s)"
            mkdir "$TEMP_DIR"
            unzip -q "$OUTPUT_FILE" -d "$TEMP_DIR"
            
            # 檢查結構
            echo "=== 目錄結構檢查 ==="
            
            # 檢查是否有 README.txt
            if [ -f "$TEMP_DIR/README.txt" ]; then
                echo "✓ 找到 README.txt"
            else
                echo "✗ 缺少 README.txt"
            fi
            
            # 檢查考生目錄結構
            AUTONO_DIRS=$(find "$TEMP_DIR" -maxdepth 1 -type d -name "[0-9]*" | wc -l)
            echo "✓ 找到 $AUTONO_DIRS 個考生目錄"
            
            # 檢查每個考生目錄的內容
            for dir in "$TEMP_DIR"/[0-9]*; do
                if [ -d "$dir" ]; then
                    AUTONO=$(basename "$dir")
                    PDF_COUNT=$(find "$dir" -name "*.pdf" | wc -l)
                    echo "  考生 $AUTONO: $PDF_COUNT 個 PDF 檔案"
                    
                    # 檢查檔案命名是否為數字
                    for pdf in "$dir"/*.pdf; do
                        if [ -f "$pdf" ]; then
                            FILENAME=$(basename "$pdf" .pdf)
                            if [[ "$FILENAME" =~ ^[0-9]+$ ]]; then
                                echo "    ✓ $FILENAME.pdf (正確的索引命名)"
                            else
                                echo "    ✗ $FILENAME.pdf (命名格式錯誤)"
                            fi
                        fi
                    done
                fi
            done
            
            # 清理臨時目錄
            rm -rf "$TEMP_DIR"
            
        else
            echo "✗ ZIP 檔案格式無效"
        fi
    else
        echo "✗ 下載的檔案為空或不存在"
    fi
else
    echo "✗ 檔案下載失敗"
fi

echo ""
echo "=== 結構測試完成 ==="

# 清理
read -p "是否要刪除下載的測試檔案? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f "$OUTPUT_FILE"
    echo "測試檔案已刪除"
fi

echo ""
echo "新的檔案結構特點:"
echo "✓ ZIP 檔案名: recommendations_{exam_id}_{exam_year}_{timestamp}.zip"
echo "✓ 目錄結構: 直接依照考生 autono 分類"
echo "✓ 檔案命名: 推薦函以索引命名 (1.pdf, 2.pdf, 3.pdf...)"
echo "✓ 無需 exam_id/exam_year 層級目錄"
