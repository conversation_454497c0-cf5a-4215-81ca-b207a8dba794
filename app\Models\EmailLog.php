<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 郵件記錄模型
 *
 * 記錄系統發送的所有郵件，包括邀請信、提醒信等
 */
class EmailLog extends Model
{
    use HasFactory;

    /**
     * 郵件類型常數
     */
    const TYPE_INVITATION = 'invitation'; // 邀請信
    const TYPE_REMINDER = 'reminder';     // 提醒信
    const TYPE_NOTIFICATION = 'notification'; // 通知信

    /**
     * 郵件狀態常數
     */
    const STATUS_PENDING = 'pending'; // 待發送
    const STATUS_SENT = 'sent';       // 已發送
    const STATUS_FAILED = 'failed';   // 發送失敗
    const STATUS_BOUNCED = 'bounced'; // 退信

    /**
     * 可批量賦值的屬性
     *
     * @var array<string>
     */
    protected $fillable = [
        'recommendation_letter_id', // 關聯推薦函 ID
        'recipient_email',          // 收件人電子郵件 (更新欄位名稱)
        'recipient_name',           // 收件人姓名
        'subject',                  // 郵件主旨
        'content',                  // 郵件內容
        'template_name',            // 模板名稱
        'email_type',              // 郵件類型
        'status',                  // 發送狀態
        'error_message',           // 錯誤訊息
        'sent_at',                 // 發送時間
        'retry_count',             // 重試次數
        'metadata',                // 額外資料
    ];

    /**
     * 屬性類型轉換
     *
     * @var array<string, string>
     */
    protected $casts = [
        'sent_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * 取得郵件記錄關聯的推薦函
     *
     * @return BelongsTo
     */
    public function recommendationLetter(): BelongsTo
    {
        return $this->belongsTo(RecommendationLetter::class);
    }

    /**
     * 檢查郵件是否已發送
     *
     * @return bool
     */
    public function isSent(): bool
    {
        return $this->status === self::STATUS_SENT;
    }

    /**
     * 檢查郵件是否發送失敗
     *
     * @return bool
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 標記郵件為已發送
     *
     * @return bool
     */
    public function markAsSent(): bool
    {
        return $this->update([
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    /**
     * 標記郵件為發送失敗
     *
     * @param string $errorMessage 錯誤訊息
     * @return bool
     */
    public function markAsFailed(string $errorMessage): bool
    {
        return $this->update([
            'status' => self::STATUS_FAILED,
            'error_message' => $errorMessage,
            'retry_count' => $this->retry_count + 1,
        ]);
    }

    /**
     * 檢查郵件是否可以重試
     *
     * @param int $maxRetries 最大重試次數
     * @return bool
     */
    public function canRetry(int $maxRetries = 3): bool
    {
        return $this->retry_count < $maxRetries && $this->isFailed();
    }

    /**
     * 取得郵件類型的中文顯示
     *
     * @return string
     */
    public function getTypeDisplayAttribute(): string
    {
        return match ($this->email_type) {
            self::TYPE_INVITATION => '邀請信',
            self::TYPE_REMINDER => '提醒信',
            self::TYPE_NOTIFICATION => '通知信',
            default => '未知類型',
        };
    }

    /**
     * 取得郵件狀態的中文顯示
     *
     * @return string
     */
    public function getStatusDisplayAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_PENDING => '待發送',
            self::STATUS_SENT => '已發送',
            self::STATUS_FAILED => '發送失敗',
            self::STATUS_BOUNCED => '退信',
            default => '未知狀態',
        };
    }

    /**
     * 記錄邀請郵件
     */
    public static function logInvitation(int $recommendationLetterId, string $email, string $name, string $subject, string $content = ''): EmailLog
    {
        return self::create([
            'recommendation_letter_id' => $recommendationLetterId,
            'recipient_email' => $email,
            'recipient_name' => $name,
            'email_type' => self::TYPE_INVITATION,
            'subject' => $subject,
            'content' => $content ?: '邀請郵件內容',
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    /**
     * 記錄提醒郵件
     */
    public static function logReminder(int $recommendationLetterId, string $email, string $name, string $subject, string $content = ''): EmailLog
    {
        return self::create([
            'recommendation_letter_id' => $recommendationLetterId,
            'recipient_email' => $email,
            'recipient_name' => $name,
            'email_type' => self::TYPE_REMINDER,
            'subject' => $subject,
            'content' => $content ?: '提醒郵件內容',
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    /**
     * 記錄通知郵件
     */
    public static function logNotification(string $email, string $name, string $subject, string $content = '', string $type = self::TYPE_NOTIFICATION): EmailLog
    {
        return self::create([
            'recipient_email' => $email,
            'recipient_name' => $name,
            'email_type' => $type,
            'subject' => $subject,
            'content' => $content ?: '通知郵件內容',
            'status' => self::STATUS_SENT,
            'sent_at' => now(),
        ]);
    }

    /**
     * 獲取統計數據
     */
    public static function getStats(): array
    {
        return [
            'total' => self::count(),
            'sent' => self::where('status', self::STATUS_SENT)->count(),
            'failed' => self::where('status', self::STATUS_FAILED)->count(),
            'pending' => self::where('status', self::STATUS_PENDING)->count(),
            'recent' => self::where('created_at', '>=', now()->subDays(7))->count(),
        ];
    }
}
